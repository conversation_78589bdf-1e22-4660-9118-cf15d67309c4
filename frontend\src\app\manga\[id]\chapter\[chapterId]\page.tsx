'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { SlideOutPanel } from '@/components/ui/slide-out-panel';
import { ReaderSettingsPanel } from '@/components/manga/ReaderSettingsPanel';
import { 
  MangaChapter,
  MangaPage,
  Manga,
  apiClient
} from '@/lib/api';
import { localStorageUtils, debounce, ImagePreloader } from '@/lib/readerUtils';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { 
  ArrowLeft,
  ArrowRight,
  ChevronLeft,
  ChevronRight,
  Home,
  BookOpen,
  Settings,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Maximize,
  Minimize,
  Sun,
  Moon,
  <PERSON>u,
  MessageSquare,
  Share2,
  Bookmark,
  MoreVertical,
  Layers,
  ScrollText
} from 'lucide-react';

type ReadingMode = 'flip' | 'scroll';

export default function EnhancedMangaChapterPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const { resolvedTheme, setTheme } = useTheme();
  
  const mangaId = params.id as string;
  const chapterNumber = params.chapterId as string;
  
  // 基础数据状态
  const [manga, setManga] = useState<Manga | null>(null);
  const [chapter, setChapter] = useState<MangaChapter | null>(null);
  const [pages, setPages] = useState<MangaPage[]>([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // UI 控制状态 - 从localStorage读取用户偏好
  const [readingMode, setReadingMode] = useState<ReadingMode>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('manga-reading-mode');
      return (saved as ReadingMode) || 'scroll';
    }
    return 'scroll';
  });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [zoom, setZoom] = useState(1);
  const [showUI, setShowUI] = useState(true);
  // 移除侧边栏，改为直接显示设置面板
  const [showSettings, setShowSettings] = useState(false);
  const [readerTheme, setReaderTheme] = useState<'default' | 'dark' | 'parchment' | 'paper' | 'sepia' | 'green' | 'blue'>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('manga-reader-theme');
      return (saved as any) || 'default';
    }
    return 'default';
  });
  const [showFocusMask, setShowFocusMask] = useState(false);

  // 连续阅读状态 - 从localStorage读取用户偏好
  const [continuousReading, setContinuousReading] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('manga-continuous-reading');
      return saved !== null ? JSON.parse(saved) : true;
    }
    return true;
  });
  const [loadedChapters, setLoadedChapters] = useState<{[key: string]: {chapter: MangaChapter, pages: MangaPage[]}}>();
  const [isLoadingNextChapter, setIsLoadingNextChapter] = useState(false);

  // 工具和引用
  const imagePreloader = useRef(new ImagePreloader());
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pageRefs = useRef<(HTMLDivElement | null)[]>([]);
  const chapterObserverRef = useRef<IntersectionObserver | null>(null);
  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null);
  const keyboardTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 防抖的进度保存函数
  const debouncedSaveProgress = useCallback(
    debounce((mangaId: number, chapterDbId: number, pageNumber: number) => {
      if (user) {
        // 登录用户保存到服务器
        apiClient.updateMangaReadingProgress(mangaId, {
          chapter_id: chapterDbId,
          page_number: pageNumber + 1,
          total_pages: pages.length,
          progress_percentage: ((pageNumber + 1) / pages.length) * 100,
        }).catch(console.error);
      } else {
        // 游客保存到本地存储
        localStorageUtils.saveGuestProgress(mangaId, chapterDbId, pageNumber);
      }
    }, 5000),
    [user, pages.length]
  );

  // 加载下一章节
  const loadNextChapter = useCallback(async (currentChapterNumber: number) => {
    if (!manga || !continuousReading || isLoadingNextChapter) return;

    const currentIndex = manga.chapters?.findIndex(ch => ch.chapter_number === currentChapterNumber) ?? -1;
    if (currentIndex === -1 || currentIndex >= (manga.chapters?.length ?? 0) - 1) return;

    const nextChapter = manga.chapters![currentIndex + 1];
    const chapterKey = `${mangaId}-${nextChapter.chapter_number}`;

    // 如果已经加载过，跳过
    if (loadedChapters?.[chapterKey]) return;

    try {
      setIsLoadingNextChapter(true);

      // 获取下一章节的页面数据
      const nextChapterData = await apiClient.getMangaChapter(parseInt(mangaId), nextChapter.chapter_number);
      const nextPages = await apiClient.getMangaChapterPages(nextChapterData.id);

      // 预加载图片
      nextPages.forEach(page => {
        if (page.image_url) {
          imagePreloader.current.preload(page.image_url);
        }
      });

      // 保存到状态
      setLoadedChapters(prev => ({
        ...prev,
        [chapterKey]: {
          chapter: nextChapterData,
          pages: nextPages
        }
      }));

    } catch (error) {
      console.error('Failed to load next chapter:', error);
    } finally {
      setIsLoadingNextChapter(false);
    }
  }, [manga, mangaId, continuousReading, isLoadingNextChapter, loadedChapters]);

  // 加载上一章节
  const loadPreviousChapter = useCallback(async (currentChapterNumber: number) => {
    if (!manga || !continuousReading || isLoadingNextChapter) return;

    const currentIndex = manga.chapters?.findIndex(ch => ch.chapter_number === currentChapterNumber) ?? -1;
    if (currentIndex === -1 || currentIndex <= 0) return;

    const prevChapter = manga.chapters![currentIndex - 1];
    const chapterKey = `${mangaId}-${prevChapter.chapter_number}`;

    // 如果已经加载过，跳过
    if (loadedChapters?.[chapterKey]) return;

    try {
      setIsLoadingNextChapter(true); // 复用loading状态

      // 获取上一章节的页面数据
      const prevChapterData = await apiClient.getMangaChapter(parseInt(mangaId), prevChapter.chapter_number);
      const prevPages = await apiClient.getMangaChapterPages(prevChapterData.id);

      // 预加载图片
      prevPages.forEach(page => {
        if (page.image_url) {
          imagePreloader.current.preload(page.image_url);
        }
      });

      // 保存到状态
      setLoadedChapters(prev => ({
        ...prev,
        [chapterKey]: {
          chapter: prevChapterData,
          pages: prevPages
        }
      }));

    } catch (error) {
      console.error('Failed to load previous chapter:', error);
    } finally {
      setIsLoadingNextChapter(false);
    }
  }, [manga, mangaId, continuousReading, isLoadingNextChapter, loadedChapters]);

  // 设置章节观察器（无缝翻页优化版）
  useEffect(() => {
    if (!continuousReading || readingMode !== 'scroll') return;

    const triggerCooldown = new Map<string, number>();
    const COOLDOWN_TIME = 2000; // 2秒冷却时间

    chapterObserverRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && entry.intersectionRatio > 0.1) {
            const chapterNumber = parseFloat(entry.target.getAttribute('data-chapter') || '0');
            const triggerKey = `${entry.target.className}-${chapterNumber}`;
            const now = Date.now();

            // 检查冷却时间
            if (triggerCooldown.has(triggerKey)) {
              const lastTrigger = triggerCooldown.get(triggerKey)!;
              if (now - lastTrigger < COOLDOWN_TIME) {
                console.log(`🔄 触发器冷却中: ${triggerKey}`);
                return;
              }
            }

            triggerCooldown.set(triggerKey, now);

            if (entry.target.classList.contains('chapter-end-trigger')) {
              console.log('📖 无缝翻页触发器激活:', chapterNumber);
              loadNextChapter(chapterNumber);
            }
          }
        });
      },
      {
        threshold: [0.1, 0.5], // 多个阈值，更精确触发
        rootMargin: '300px 0px', // 增加预加载距离，提前触发
        root: scrollContainerRef.current
      }
    );

    // 动态观察触发器元素
    const observeTriggers = () => {
      const triggers = document.querySelectorAll('.chapter-end-trigger');
      triggers.forEach(trigger => {
        if (chapterObserverRef.current) {
          chapterObserverRef.current.observe(trigger);
        }
      });
      console.log(`🔍 观察 ${triggers.length} 个无缝翻页触发器`);
    };

    // 初始观察
    observeTriggers();

    // 监听DOM变化，动态添加新的触发器
    const mutationObserver = new MutationObserver(() => {
      observeTriggers();
    });

    if (scrollContainerRef.current) {
      mutationObserver.observe(scrollContainerRef.current, {
        childList: true,
        subtree: true
      });
    }

    return () => {
      chapterObserverRef.current?.disconnect();
      mutationObserver.disconnect();
    };
  }, [continuousReading, readingMode, loadNextChapter]);

  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 防止在输入框中触发
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (e.key) {
        case 'ArrowLeft':
        case 'a':
        case 'A':
          e.preventDefault();
          if (readingMode === 'flip') {
            goToPreviousPage();
          } else {
            // 滚动模式下向上滚动
            scrollContainerRef.current?.scrollBy({ top: -window.innerHeight * 0.8, behavior: 'smooth' });
          }
          break;

        case 'ArrowRight':
        case 'd':
        case 'D':
        case ' ': // 空格键
          e.preventDefault();
          if (readingMode === 'flip') {
            goToNextPage();
          } else {
            // 滚动模式下向下滚动
            scrollContainerRef.current?.scrollBy({ top: window.innerHeight * 0.8, behavior: 'smooth' });
          }
          break;

        case 'ArrowUp':
        case 'w':
        case 'W':
          e.preventDefault();
          if (readingMode === 'scroll') {
            scrollContainerRef.current?.scrollBy({ top: -200, behavior: 'smooth' });
          }
          break;

        case 'ArrowDown':
        case 's':
        case 'S':
          e.preventDefault();
          if (readingMode === 'scroll') {
            scrollContainerRef.current?.scrollBy({ top: 200, behavior: 'smooth' });
          }
          break;

        case 'Home':
          e.preventDefault();
          if (readingMode === 'flip') {
            setCurrentPage(0);
          } else {
            scrollContainerRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
          }
          break;

        case 'End':
          e.preventDefault();
          if (readingMode === 'flip') {
            setCurrentPage(pages.length - 1);
          } else {
            scrollContainerRef.current?.scrollTo({
              top: scrollContainerRef.current.scrollHeight,
              behavior: 'smooth'
            });
          }
          break;

        case 'f':
        case 'F':
          e.preventDefault();
          toggleFullscreen();
          break;

        case 'Escape':
          e.preventDefault();
          if (isFullscreen) {
            setIsFullscreen(false);
          }
          if (showSettings) {
            setShowSettings(false);
          }
          break;

        case '=':
        case '+':
          e.preventDefault();
          setZoom(prev => Math.min(3, prev + 0.25));
          break;

        case '-':
          e.preventDefault();
          setZoom(prev => Math.max(0.5, prev - 0.25));
          break;

        case '0':
          e.preventDefault();
          setZoom(1);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [readingMode, pages.length, isFullscreen, showSettings]);

  // 触摸手势支持
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (e.touches.length === 1) {
      const touch = e.touches[0];
      touchStartRef.current = {
        x: touch.clientX,
        y: touch.clientY,
        time: Date.now()
      };
    }
  }, []);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    if (!touchStartRef.current || e.changedTouches.length !== 1) return;

    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - touchStartRef.current.x;
    const deltaY = touch.clientY - touchStartRef.current.y;
    const deltaTime = Date.now() - touchStartRef.current.time;

    // 检查是否为有效的滑动手势
    const minDistance = 30; // 降低最小距离，提高移动端敏感度
    const maxTime = 500; // 增加最大时间，适应移动端操作

    if (deltaTime > maxTime) return;

    const absX = Math.abs(deltaX);
    const absY = Math.abs(deltaY);

    if (readingMode === 'flip') {
      // 翻页模式：水平滑动
      if (absX > minDistance && absX > absY * 1.5) { // 降低比例要求
        e.preventDefault();
        if (deltaX > 0) {
          goToPreviousPage(); // 向右滑动，上一页
        } else {
          goToNextPage(); // 向左滑动，下一页
        }
      }
    } else {
      // 滚动模式：改进垂直滑动
      if (absY > minDistance && absY > absX * 1.2) { // 降低比例要求
        e.preventDefault();
        const scrollAmount = deltaY > 0 ? -window.innerHeight * 0.6 : window.innerHeight * 0.6;
        scrollContainerRef.current?.scrollBy({
          top: scrollAmount,
          behavior: 'smooth'
        });
      }
      // 水平滑动在滚动模式下也可以翻页（移动端友好）
      else if (absX > minDistance * 1.5 && absX > absY * 2) {
        e.preventDefault();
        if (deltaX > 0) {
          goToPreviousPage();
        } else {
          goToNextPage();
        }
      }
    }

    touchStartRef.current = null;
  }, [readingMode]);

  // 数据获取
  const fetchChapterData = async () => {
    try {
      setLoading(true);
      
      const mangaData = await apiClient.getManga(parseInt(mangaId));
      setManga(mangaData);
      
      const chapterData = await apiClient.getMangaChapterByNumber(
        parseInt(mangaId), 
        parseFloat(chapterNumber)
      );
      setChapter(chapterData);
      
      const pagesData = await apiClient.getChapterPages(chapterData.id);
      setPages(pagesData.pages);
      
      // 加载阅读进度
      let startPage = 0;
      if (user) {
        try {
          const progress = await apiClient.getMangaReadingProgress(parseInt(mangaId));
          if (progress.chapter_id === chapterData.id) {
            startPage = progress.page_number - 1;
          }
        } catch (error) {
          console.log('No server progress found');
        }
      } else {
        const guestProgress = localStorageUtils.getGuestProgress(parseInt(mangaId));
        if (guestProgress && guestProgress.chapterId === chapterData.id) {
          startPage = guestProgress.pageNumber;
        }
      }
      
      setCurrentPage(Math.max(0, Math.min(startPage, pagesData.pages.length - 1)));
      
    } catch (error) {
      console.error('Failed to fetch chapter data:', error);
      setError('获取章节信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 图片预加载
  useEffect(() => {
    if (pages.length === 0) return;

    const preloadRange = 3; // 预加载前后3页
    const startIdx = Math.max(0, currentPage - preloadRange);
    const endIdx = Math.min(pages.length - 1, currentPage + preloadRange);
    
    const urlsToPreload = pages.slice(startIdx, endIdx + 1).map(page => page.image_url);
    
    // 当前页高优先级，其他页低优先级
    const currentUrl = pages[currentPage]?.image_url;
    if (currentUrl) {
      imagePreloader.current.preloadImages([currentUrl], 'high');
    }
    
    const otherUrls = urlsToPreload.filter(url => url !== currentUrl);
    if (otherUrls.length > 0) {
      imagePreloader.current.preloadImages(otherUrls, 'low');
    }
  }, [currentPage, pages]);

  // 进度保存
  useEffect(() => {
    if (pages.length > 0 && chapter) {
      debouncedSaveProgress(parseInt(mangaId), chapter.id, currentPage);
    }
  }, [currentPage, mangaId, chapter, debouncedSaveProgress, pages.length]);

  // 保存阅读模式偏好
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('manga-reading-mode', readingMode);
      console.log('💾 阅读模式已保存:', readingMode);
    }
  }, [readingMode]);

  // 保存阅读主题偏好
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('manga-reader-theme', readerTheme);
      console.log('💾 阅读主题已保存:', readerTheme);
    }
  }, [readerTheme]);

  // 保存连续阅读偏好
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('manga-continuous-reading', JSON.stringify(continuousReading));
      console.log('💾 连续阅读设置已保存:', continuousReading);
    }
  }, [continuousReading]);

  // 滚动模式下的页面跟踪
  useEffect(() => {
    if (readingMode !== 'scroll' || pages.length === 0) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const pageIndex = parseInt(entry.target.getAttribute('data-page-index') || '0');
            setCurrentPage(pageIndex);
          }
        });
      },
      {
        threshold: 0.5, // 当页面有50%可见时算作当前页
        root: scrollContainerRef.current,
      }
    );

    pageRefs.current.forEach((ref) => {
      if (ref) {
        observer.observe(ref);
      }
    });

    return () => {
      observer.disconnect();
    };
  }, [readingMode, pages.length]);

  // 键盘控制
  const handleKeyPress = useCallback((e: KeyboardEvent) => {
    if (showSettings) return; // 设置面板打开时禁用键盘控制

    switch (e.key) {
      case 'ArrowLeft':
      case 'a':
      case 'A':
        e.preventDefault();
        handlePrevPage();
        break;
      case 'ArrowRight':
      case 'd':
      case 'D':
        e.preventDefault();
        handleNextPage();
        break;
      case 'ArrowUp':
        e.preventDefault();
        if (readingMode === 'scroll') {
          scrollContainerRef.current?.scrollBy({ top: -200, behavior: 'smooth' });
        }
        break;
      case 'ArrowDown':
        e.preventDefault();
        if (readingMode === 'scroll') {
          scrollContainerRef.current?.scrollBy({ top: 200, behavior: 'smooth' });
        }
        break;
      case 'Escape':
        e.preventDefault();
        if (isFullscreen) {
          exitFullscreen();
        } else if (showSettings) {
          setShowSettings(false);
        }
        break;
      case ' ':
        e.preventDefault();
        setShowUI(!showUI);
        break;
    }
  }, [readingMode, isFullscreen, showSettings]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);

  // UI显隐控制 - 点击切换
  const handleContentClick = useCallback((e: React.MouseEvent) => {
    // 如果点击的是按钮或其他交互元素，不处理
    if ((e.target as HTMLElement).closest('button') || 
        (e.target as HTMLElement).closest('a') ||
        (e.target as HTMLElement).closest('.no-toggle')) {
      return;
    }
    
    // 切换UI显示状态
    setShowUI(!showUI);
  }, [showUI]);

  // 初始化时显示UI
  useEffect(() => {
    setShowUI(true);
  }, []);

  // 页面导航
  const handlePrevPage = () => {
    if (readingMode === 'scroll') {
      scrollContainerRef.current?.scrollBy({ top: -window.innerHeight * 0.8, behavior: 'smooth' });
    } else {
      if (currentPage > 0) {
        setCurrentPage(currentPage - 1);
      } else {
        handlePrevChapter();
      }
    }
  };

  const handleNextPage = () => {
    if (readingMode === 'scroll') {
      scrollContainerRef.current?.scrollBy({ top: window.innerHeight * 0.8, behavior: 'smooth' });
    } else {
      if (currentPage < pages.length - 1) {
        setCurrentPage(currentPage + 1);
      } else {
        handleNextChapter();
      }
    }
  };

  const handlePrevChapter = () => {
    if (manga && manga.chapters && chapter) {
      const currentIndex = manga.chapters.findIndex(ch => ch.chapter_number === chapter.chapter_number);
      if (currentIndex > 0) {
        const prevChapter = manga.chapters[currentIndex - 1];
        router.push(`/manga/${mangaId}/chapter/${prevChapter.chapter_number}`);
      }
    }
  };

  const handleNextChapter = () => {
    if (manga && manga.chapters && chapter) {
      const currentIndex = manga.chapters.findIndex(ch => ch.chapter_number === chapter.chapter_number);
      if (currentIndex < manga.chapters.length - 1) {
        const nextChapter = manga.chapters[currentIndex + 1];
        router.push(`/manga/${mangaId}/chapter/${nextChapter.chapter_number}`);
      }
    }
  };

  // 全屏控制
  const toggleFullscreen = async () => {
    try {
      if (!isFullscreen) {
        await document.documentElement.requestFullscreen?.();
        setIsFullscreen(true);
      } else {
        await document.exitFullscreen?.();
        setIsFullscreen(false);
      }
    } catch (error) {
      console.error('Fullscreen toggle failed:', error);
      setIsFullscreen(!isFullscreen);
    }
  };

  const exitFullscreen = async () => {
    try {
      if (document.fullscreenElement) {
        await document.exitFullscreen();
      }
      setIsFullscreen(false);
    } catch (error) {
      console.error('Exit fullscreen failed:', error);
    }
  };


  // 缩放控制
  const handleZoomIn = () => setZoom(Math.min(zoom + 0.25, 3));
  const handleZoomOut = () => setZoom(Math.max(zoom - 0.25, 0.5));
  const resetZoom = () => setZoom(1);
  const fitToWidth = () => {
    if (!pages[currentPage]) return;
    // 计算适配宽度的缩放比例
    const containerWidth = window.innerWidth - 32; // 减去左右padding
    setZoom(1); // 先重置为1，让图片自适应
  };
  const fitToHeight = () => {
    if (!pages[currentPage]) return;
    // 计算适配高度的缩放比例
    const containerHeight = window.innerHeight - 32;
    setZoom(1); // 先重置为1，让图片自适应
  };
  
  // 双击缩放
  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    if (zoom === 1) {
      setZoom(2);
    } else {
      setZoom(1);
    }
  }, [zoom]);

  // 阅读器主题处理
  useEffect(() => {
    // 根据阅读器主题设置页面样式
    const themeClasses = ['reader-default', 'reader-dark', 'reader-parchment', 'reader-paper', 'reader-sepia', 'reader-green', 'reader-blue'];
    document.body.classList.remove(...themeClasses);
    document.body.classList.add(`reader-${readerTheme}`);

    // 如果是暗色主题，确保系统也是暗色
    if (readerTheme === 'dark' && resolvedTheme !== 'dark') {
      setTheme('dark');
    } else if (readerTheme !== 'dark' && resolvedTheme === 'dark') {
      setTheme('light');
    }

    return () => {
      document.body.classList.remove(...themeClasses);
    };
  }, [readerTheme, resolvedTheme, setTheme]);

  // 书签功能
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const handleBookmark = async () => {
    try {
      if (user) {
        // 登录用户，保存到服务器
        await apiClient.addBookmark({
          manga_id: parseInt(mangaId),
          chapter_id: chapter?.id || 0,
          page_number: currentPage + 1,
          bookmark_name: `第${chapter?.chapter_number}话 - 第${currentPage + 1}页`,
          notes: manga?.title || ''
        });
      } else {
        // 游客，保存到本地
        // 游客书签保存到本地
        const bookmarkKey = `manga_bookmark_${mangaId}_${chapter?.id}_${currentPage}`;
        localStorage.setItem(bookmarkKey, JSON.stringify({
          mangaId: parseInt(mangaId),
          chapterId: chapter?.id || 0,
          pageNumber: currentPage,
          timestamp: Date.now()
        }));
      }
      setIsBookmarked(!isBookmarked);
      setShowToast(true);
      setTimeout(() => setShowToast(false), 1500);
    } catch (error) {
      console.error('Failed to bookmark:', error);
    }
  };

  useEffect(() => {
    if (mangaId && chapterNumber) {
      fetchChapterData();
    }
  }, [mangaId, chapterNumber]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">加载中...</p>
        </div>
      </div>
    );
  }

  if (error || !chapter || !manga) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">章节不存在</h2>
          <p className="mb-4 text-muted-foreground">{error || '未找到该章节'}</p>
          <Button onClick={() => router.push(`/manga/${mangaId}`)}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回漫画详情
          </Button>
        </div>
      </div>
    );
  }

  const currentChapterIndex = manga.chapters?.findIndex(ch => ch.chapter_number === chapter?.chapter_number) ?? -1;
  const hasNextChapter = currentChapterIndex < (manga.chapters?.length ?? 0) - 1;
  const hasPrevChapter = currentChapterIndex > 0;

  // 根据主题获取背景样式
  const getThemeClasses = () => {
    switch (readerTheme) {
      case 'dark':
        return 'bg-gray-900 text-gray-100';
      case 'sepia':
        return 'bg-amber-50 text-amber-900';
      case 'green':
        return 'bg-green-50 text-green-900';
      case 'blue':
        return 'bg-blue-50 text-blue-900';
      case 'parchment':
        return 'bg-gradient-to-br from-amber-50 via-orange-50 to-amber-100 text-amber-900';
      case 'paper':
        return 'bg-stone-50 text-stone-800';
      default:
        return 'bg-background text-foreground';
    }
  };

  return (
    <div
      className={`manga-reader-container min-h-screen transition-all duration-500 immersive-reading overflow-hidden ${
        isFullscreen ? 'fixed inset-0 z-50' : 'fixed inset-0'
      } ${getThemeClasses()}`}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
    >
      {/* 顶部极细进度条 */}
      <div className={`fixed top-0 left-0 right-0 z-50 h-[2px] transition-opacity duration-300 ${
        showUI ? 'opacity-100' : 'opacity-0'
      }`}>
        <div 
          className="h-full bg-primary transition-all duration-200"
          style={{ width: `${((currentPage + 1) / pages.length) * 100}%` }}
        />
      </div>
      
      {/* 顶部栏 */}
      <div className={`fixed top-0 left-0 right-0 z-40 bg-background/90 backdrop-blur-md border-b manga-float-shadow rounded-b-2xl transition-all duration-300 ${
        showUI ? 'translate-y-0' : '-translate-y-full'
      }`}>
        <div className="flex items-center justify-between p-4">
          {/* 左侧区域 */}
          <div className="flex items-center gap-2 flex-1">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => router.push(`/manga/${mangaId}`)}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              退出
            </Button>
            
            {/* 章节导航 */}
            <Button 
              variant="ghost" 
              size="sm"
              onClick={handlePrevChapter}
              disabled={!hasPrevChapter}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <Button 
              variant="ghost" 
              size="sm"
              onClick={handleNextChapter}
              disabled={!hasNextChapter}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          {/* 中间区域 - 页码显示 */}
          <div className="flex items-center justify-center flex-1 px-4">
            <div className="text-center">
              <div className="font-mono text-sm font-medium">
                {currentPage + 1} / {pages.length}
              </div>
              <div className="text-xs text-muted-foreground">
                第 {chapter.chapter_number} 话
                {chapter.title && ` - ${chapter.title}`}
              </div>
            </div>
          </div>

          {/* 右侧区域 */}
          <div className="flex items-center gap-2 flex-1 justify-end">
            {/* 书签 */}
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleBookmark}
              className={isBookmarked ? 'text-primary' : ''}
            >
              <Bookmark className={`h-4 w-4 ${isBookmarked ? 'fill-current' : ''}`} />
            </Button>

            {/* 全屏 */}
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={toggleFullscreen}
            >
              {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
            </Button>

            {/* 阅读模式切换 */}
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setReadingMode(readingMode === 'flip' ? 'scroll' : 'flip')}
              title={`切换到${readingMode === 'flip' ? '滚动' : '翻页'}模式`}
            >
              {readingMode === 'flip' ? <ScrollText className="h-4 w-4" /> : <Layers className="h-4 w-4" />}
            </Button>

            {/* 设置按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(true)}
              title="阅读设置"
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* 阅读区域 */}
      <div className="h-screen overflow-hidden">
        {readingMode === 'flip' ? (
          // 翻页模式
          <div className="relative h-full flex items-center justify-center p-4" onClick={handleContentClick}>
            {pages.length > 0 && (
              <div 
                className="relative max-w-full max-h-full flex items-center justify-center"
                style={{ 
                  transform: `scale(${zoom})`, 
                  transformOrigin: 'center',
                  transition: 'transform 0.2s ease-out'
                }}
                onDoubleClick={handleDoubleClick}
              >
                <img
                  key={currentPage}
                  src={pages[currentPage]?.image_url}
                  alt={`第 ${currentPage + 1} 页`}
                  className="max-w-full max-h-full object-contain select-none manga-page-transition manga-page-shadow"
                  draggable={false}
                  loading="eager"
                />
                
                {/* 左右导航区域 */}
                <div 
                  className="absolute left-0 top-0 w-1/3 h-full cursor-pointer flex items-center justify-start pl-4 opacity-0 hover:opacity-100 transition-opacity no-toggle"
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePrevPage();
                  }}
                >
                  {(currentPage > 0 || hasPrevChapter) && (
                    <div className="bg-black/40 backdrop-blur-sm p-3 rounded-2xl shadow-xl">
                      <ChevronLeft className="h-6 w-6 text-white" />
                    </div>
                  )}
                </div>
                
                <div 
                  className="absolute right-0 top-0 w-1/3 h-full cursor-pointer flex items-center justify-end pr-4 opacity-0 hover:opacity-100 transition-opacity no-toggle"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleNextPage();
                  }}
                >
                  {(currentPage < pages.length - 1 || hasNextChapter) && (
                    <div className="bg-black/40 backdrop-blur-sm p-3 rounded-2xl shadow-xl">
                      <ChevronRight className="h-6 w-6 text-white" />
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          // 滚动模式
          <div
            ref={scrollContainerRef}
            className="h-full overflow-y-auto manga-reader-scroll"
            onClick={handleContentClick}
            style={{
              paddingTop: showUI ? '64px' : '0',
              transition: 'padding 0.3s ease',
              // 移动端优化
              WebkitOverflowScrolling: 'touch', // iOS 滚动优化
              touchAction: 'pan-y', // 只允许垂直滚动
              overscrollBehavior: 'contain', // 防止过度滚动
              scrollBehavior: 'smooth'
            }}
          >
            <div className="flex flex-col items-center gap-2 pb-20">
              {/* 当前章节的页面 */}
              {pages.map((page, index) => (
                <div
                  key={`current-${index}`}
                  ref={(el) => (pageRefs.current[index] = el)}
                  className="w-full max-w-4xl flex justify-center"
                  data-page={index}
                >
                  <img
                    src={page.image_url}
                    alt={`第 ${chapter?.chapter_number} 话 第 ${index + 1} 页`}
                    className="w-full block select-none"
                    draggable={false}
                    loading={index < 3 ? "eager" : "lazy"}
                    style={{
                      maxHeight: '100vh',
                      objectFit: 'contain'
                    }}
                  />
                </div>
              ))}

              {/* 章节结束分隔符和下一章节加载触发器 */}
              {continuousReading && hasNextChapter && (
                <div className="w-full max-w-4xl py-8">
                  <div className="text-center border-t border-b border-border py-6 mb-6">
                    <h3 className="text-lg font-semibold mb-2">第 {chapter?.chapter_number} 话 完</h3>
                    <p className="text-sm text-muted-foreground">继续下拉阅读下一话</p>
                  </div>

                  {/* 下一章节触发器 */}
                  <div
                    className="chapter-end-trigger w-full h-32 flex items-center justify-center"
                    data-chapter={chapter?.chapter_number}
                    ref={(el) => {
                      if (el && chapterObserverRef.current) {
                        chapterObserverRef.current.observe(el);
                      }
                    }}
                  >
                    {isLoadingNextChapter ? (
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                        <p className="text-sm text-muted-foreground">正在加载下一话...</p>
                      </div>
                    ) : (
                      <div className="text-center">
                        <p className="text-sm text-muted-foreground">下拉加载下一话</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* 已加载的下一章节页面 */}
              {continuousReading && loadedChapters && Object.entries(loadedChapters)
                .filter(([chapterKey]) => {
                  const chapterNumber = parseFloat(chapterKey.split('-')[1]);
                  return chapterNumber > (chapter?.chapter_number || 0);
                })
                .sort((a, b) => {
                  const aNum = parseFloat(a[0].split('-')[1]);
                  const bNum = parseFloat(b[0].split('-')[1]);
                  return aNum - bNum;
                })
                .map(([chapterKey, chapterData]) => (
                  <div key={chapterKey} className="w-full">
                    {/* 下一章节标题 */}
                    <div className="text-center py-8 px-4 border-t border-border">
                      <h2 className="text-xl font-bold mb-2">{chapterData.chapter.title}</h2>
                      <p className="text-sm text-muted-foreground">
                        第 {chapterData.chapter.chapter_number} 话 • 共 {chapterData.pages.length} 页
                      </p>
                    </div>

                    {/* 下一章节页面 */}
                    {chapterData.pages.map((page, index) => (
                      <div
                        key={`${chapterKey}-${page.id}`}
                        className="w-full max-w-4xl flex justify-center mb-2"
                      >
                        <img
                          src={page.image_url}
                          alt={`第 ${chapterData.chapter.chapter_number} 话 第 ${index + 1} 页`}
                          className="w-full block select-none"
                          draggable={false}
                          loading="lazy"
                          style={{
                            maxHeight: '100vh',
                            objectFit: 'contain'
                          }}
                        />
                      </div>
                    ))}

                    {/* 下一章节结束，继续加载更多章节的触发器 */}
                    {hasNextChapter && (
                      <div
                        className="chapter-end-trigger w-full h-32 flex items-center justify-center"
                        data-chapter={chapterData.chapter.chapter_number}
                        ref={(el) => {
                          if (el && chapterObserverRef.current) {
                            chapterObserverRef.current.observe(el);
                          }
                        }}
                      >
                        <div className="text-center border-t border-b border-border py-6">
                          <p className="text-sm text-muted-foreground">第 {chapterData.chapter.chapter_number} 话完</p>
                          <p className="text-xs text-muted-foreground mt-1">继续下拉阅读更多章节</p>
                        </div>
                      </div>
                    )}
                  </div>
                ))}

              {/* 上一章触发器（只在当前章节前显示） */}
              {continuousReading && hasPrevChapter && (
                <div
                  className="chapter-start-trigger w-full h-20 flex items-center justify-center"
                  data-chapter={chapter?.chapter_number}
                  ref={(el) => {
                    if (el && chapterObserverRef.current) {
                      chapterObserverRef.current.observe(el);
                      console.log('Observing chapter start trigger for chapter:', chapter?.chapter_number);
                    }
                  }}
                >
                  {isLoadingNextChapter ? (
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                      <p className="text-sm text-muted-foreground">正在加载上一章...</p>
                    </div>
                  ) : (
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground">第 {chapter?.chapter_number} 话开始</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        向上滚动加载上一章
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* 当前章节 */}
              <div className="w-full">
                {/* 章节标题 */}
                <div className="text-center py-6 px-4">
                  <h2 className="text-xl font-bold mb-2">{chapter?.title}</h2>
                  <p className="text-sm text-muted-foreground">
                    第 {chapter?.chapter_number} 话 • 共 {pages.length} 页
                  </p>
                </div>

                {/* 当前章节页面 */}
                {pages.map((page, index) => (
                  <div
                    key={page.id}
                    ref={(el) => { pageRefs.current[index] = el; }}
                    data-page-index={index}
                    className="relative w-full max-w-4xl mx-auto px-4 mb-2"
                    style={{
                      transform: `scale(${zoom})`,
                      transformOrigin: 'top center',
                      marginBottom: zoom > 1 ? `${(zoom - 1) * 50}%` : '2px'
                    }}
                  >
                    <img
                      src={page.image_url}
                      alt={`第 ${index + 1} 页`}
                      className="w-full block select-none"
                      draggable={false}
                      loading={Math.abs(index - currentPage) <= 2 ? "eager" : "lazy"}
                    />
                  </div>
                ))}

                {/* 章节结束触发器 */}
                {continuousReading && hasNextChapter && (
                  <div
                    className="chapter-end-trigger w-full h-20 flex items-center justify-center"
                    data-chapter={chapter?.chapter_number}
                    ref={(el) => {
                      if (el && chapterObserverRef.current) {
                        chapterObserverRef.current.observe(el);
                        console.log('Observing chapter end trigger for chapter:', chapter?.chapter_number);
                      }
                    }}
                  >
                    {isLoadingNextChapter ? (
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                        <p className="text-sm text-muted-foreground">正在加载下一章...</p>
                      </div>
                    ) : (
                      <div className="text-center">
                        <p className="text-sm text-muted-foreground">第 {chapter?.chapter_number} 话结束</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          向下滚动加载下一章
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* 已加载的后续章节（按章节号升序） */}
              {continuousReading && loadedChapters && Object.entries(loadedChapters)
                .filter(([chapterKey]) => {
                  const chapterNumber = parseInt(chapterKey.split('-')[1]);
                  return chapterNumber > (chapter?.chapter_number || 0);
                })
                .sort((a, b) => {
                  const aNum = parseInt(a[0].split('-')[1]);
                  const bNum = parseInt(b[0].split('-')[1]);
                  return aNum - bNum;
                })
                .map(([chapterKey, chapterData]) => (
                  <div key={chapterKey} className="w-full border-t-2 border-dashed border-muted-foreground/20">
                    {/* 下一章节标题 */}
                    <div className="text-center py-6 px-4">
                      <h2 className="text-xl font-bold mb-2">{chapterData.chapter.title}</h2>
                      <p className="text-sm text-muted-foreground">
                        第 {chapterData.chapter.chapter_number} 话 • 共 {chapterData.pages.length} 页
                      </p>
                    </div>

                    {/* 下一章节页面 */}
                    {chapterData.pages.map((page, index) => (
                      <div
                        key={`${chapterKey}-${page.id}`}
                        className="relative w-full max-w-4xl mx-auto px-4 mb-2"
                        style={{
                          transform: `scale(${zoom})`,
                          transformOrigin: 'top center',
                          marginBottom: zoom > 1 ? `${(zoom - 1) * 50}%` : '2px'
                        }}
                      >
                        <img
                          src={page.image_url}
                          alt={`第 ${chapterData.chapter.chapter_number} 话 第 ${index + 1} 页`}
                          className="w-full block select-none"
                          draggable={false}
                          loading="lazy"
                        />
                      </div>
                    ))}

                    {/* 下一章节结束触发器 */}
                    <div
                      className="chapter-end-trigger w-full h-20 flex items-center justify-center"
                      data-chapter={chapterData.chapter.chapter_number}
                      ref={(el) => {
                        if (el && chapterObserverRef.current) {
                          chapterObserverRef.current.observe(el);
                        }
                      }}
                    >
                      <div className="text-center">
                        <p className="text-sm text-muted-foreground">第 {chapterData.chapter.chapter_number} 话结束</p>
                        {hasNextChapter && (
                          <p className="text-xs text-muted-foreground mt-1">
                            继续滚动加载下一章
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        )}
      </div>


      {/* 移除章节列表侧边栏，简化界面 */}

      {/* 移除评论侧边栏，简化界面 */}

      {/* 浮动操作按钮 (FAB) */}
      {!showUI && !showSettings && (
        <div className="fixed bottom-6 right-6 z-30 flex flex-col gap-3">
          <Button
            variant="default"
            size="icon"
            className="h-12 w-12 rounded-full manga-float-shadow"
            onClick={() => setShowUI(true)}
          >
            <Menu className="h-5 w-5" />
          </Button>
        </div>
      )}
      
      {/* 设置面板 */}
      <ReaderSettingsPanel
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        theme={readerTheme}
        onThemeChange={setReaderTheme}
        readingMode={readingMode}
        onReadingModeChange={setReadingMode}
        showFocusMask={showFocusMask}
        onFocusMaskChange={setShowFocusMask}
        continuousReading={continuousReading}
        onContinuousReadingChange={setContinuousReading}
        zoom={zoom}
        onZoomChange={setZoom}
        onFitToWidth={fitToWidth}
        onFitToHeight={fitToHeight}
        onResetZoom={resetZoom}
      />
      
      {/* 聚焦遮罩 */}
      {showFocusMask && (
        <div className="reading-focus-mask" />
      )}
      
      {/* Toast 提示 */}
      {showToast && (
        <div className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 animate-fade-in">
          <div className="bg-primary text-primary-foreground px-4 py-2 rounded-full shadow-lg">
            <p className="text-sm font-medium">
              {isBookmarked ? '已添加书签' : '已取消书签'}
            </p>
          </div>
        </div>
      )}
      
      {/* 长按菜单 - 移动端 */}
      <style jsx>{`
        @media (max-width: 768px) {
          .manga-page-img {
            -webkit-touch-callout: default;
          }
        }
      `}</style>
    </div>
  );
}