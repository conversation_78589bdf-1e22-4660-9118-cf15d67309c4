'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Manga, MangaListResponse, MangaSearchParams, MangaType, MangaStatus, apiClient } from '@/lib/api';
import { Search, Filter, Grid, List, Eye, Heart } from 'lucide-react';
import Link from 'next/link';
import SafeImage from '@/components/ui/safe-image';

export default function MangaListPage() {
  const [mangas, setMangas] = useState<Manga[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<string>('created_at');
  const [sortOrder, setSortOrder] = useState<'desc' | 'asc'>('desc');
  const [mangaType, setMangaType] = useState<MangaType | ''>('');
  const [status, setStatus] = useState<MangaStatus | ''>('');

  const limit = 24;

  useEffect(() => {
    fetchMangas();
  }, [currentPage, sortBy, sortOrder, mangaType, status]);

  const fetchMangas = async () => {
    try {
      setLoading(true);
      
      const params: MangaSearchParams = {
        skip: (currentPage - 1) * limit,
        limit,
        search: searchTerm || undefined,
        sort_by: sortBy,
        sort_order: sortOrder,
        manga_type: mangaType || undefined,
        status: status || undefined,
      };

      const response: MangaListResponse = await apiClient.getMangas(params);
      
      setMangas(response.mangas || []);
      setTotalCount(response.total || 0);
      setTotalPages(Math.ceil((response.total || 0) / limit));
    } catch (error) {
      console.error('Failed to fetch mangas:', error);
      setMangas([]);
      setTotalCount(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchMangas();
  };

  const handleFilterChange = () => {
    setCurrentPage(1);
    fetchMangas();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getStatusText = (status: MangaStatus) => {
    const statusMap = {
      ongoing: '连载中',
      completed: '已完结',
      hiatus: '休载',
      cancelled: '已取消'
    };
    return statusMap[status] || status;
  };

  const getTypeText = (type: MangaType) => {
    const typeMap = {
      serial: '连载',
      tankoubon: '单行本',
      doujinshi: '同人志'
    };
    return typeMap[type] || type;
  };

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">漫画</h1>
          <p className="text-muted-foreground mt-1">
            共 {totalCount} 部漫画
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="icon"
            onClick={() => setViewMode('grid')}
          >
            <Grid className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="icon"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-card border rounded-lg p-4 space-y-4">
        <form onSubmit={handleSearch} className="flex gap-2">
          <div className="flex-1">
            <Input
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索漫画标题..."
              className="w-full"
            />
          </div>
          <Button type="submit">
            <Search className="h-4 w-4 mr-2" />
            搜索
          </Button>
        </form>

        <div className="flex flex-wrap gap-4">
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">类型:</label>
            <Select value={mangaType} onValueChange={(value: MangaType | '') => { setMangaType(value); handleFilterChange(); }}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="全部" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="serial">连载</SelectItem>
                <SelectItem value="tankoubon">单行本</SelectItem>
                <SelectItem value="doujinshi">同人志</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">状态:</label>
            <Select value={status} onValueChange={(value: MangaStatus | '') => { setStatus(value); handleFilterChange(); }}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="全部" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="ongoing">连载中</SelectItem>
                <SelectItem value="completed">已完结</SelectItem>
                <SelectItem value="hiatus">休载</SelectItem>
                <SelectItem value="cancelled">已取消</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">排序:</label>
            <Select value={sortBy} onValueChange={(value: string) => { setSortBy(value); handleFilterChange(); }}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at">创建时间</SelectItem>
                <SelectItem value="updated_at">更新时间</SelectItem>
                <SelectItem value="view_count">浏览量</SelectItem>
                <SelectItem value="favorite_count">收藏量</SelectItem>
                <SelectItem value="title">标题</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <Select value={sortOrder} onValueChange={(value: 'asc' | 'desc') => { setSortOrder(value); handleFilterChange(); }}>
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desc">降序</SelectItem>
                <SelectItem value="asc">升序</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* 漫画列表 */}
      {loading ? (
        <div className="text-center py-12">
          <div className="text-lg">加载中...</div>
        </div>
      ) : mangas.length > 0 ? (
        <>
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {mangas.map((manga) => (
                <Link 
                  key={manga.id} 
                  href={`/manga/${manga.id}`}
                  className="group block"
                >
                  <div className="bg-card border rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="aspect-[3/4] relative">
                      <SafeImage
                        src={manga.cover || '/placeholder-manga.jpg'}
                        alt={manga.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                        {getStatusText(manga.status)}
                      </div>
                    </div>
                    <div className="p-3">
                      <h3 className="font-medium text-sm text-center line-clamp-1 min-h-[1.25rem] leading-tight">
                        {manga.title}
                      </h3>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {mangas.map((manga) => (
                <Link 
                  key={manga.id} 
                  href={`/manga/${manga.id}`}
                  className="group"
                >
                  <div className="bg-card border rounded-lg p-4 hover:shadow-lg transition-shadow">
                    <div className="flex gap-4">
                      <div className="w-20 h-28 relative flex-shrink-0">
                        <SafeImage
                          src={manga.cover || '/placeholder-manga.jpg'}
                          alt={manga.title}
                          fill
                          className="object-cover rounded"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h3 className="font-semibold text-lg group-hover:text-primary transition-colors">
                              {manga.title}
                            </h3>
                            {manga.title_original && (
                              <p className="text-sm text-muted-foreground">
                                {manga.title_original}
                              </p>
                            )}
                          </div>
                          <div className="flex gap-2">
                            {manga.manga_type && (
                              <span className="bg-secondary text-secondary-foreground text-xs px-2 py-1 rounded">
                                {getTypeText(manga.manga_type)}
                              </span>
                            )}
                            <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded">
                              {getStatusText(manga.status)}
                            </span>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                          {manga.author && <span>作者: {manga.author}</span>}
                          {manga.artist && <span>画师: {manga.artist}</span>}
                          {manga.release_date && (
                            <span>发布: {formatDate(manga.release_date)}</span>
                          )}
                        </div>
                        
                        {manga.description && (
                          <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                            {manga.description}
                          </p>
                        )}
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 text-sm">
                            <div className="flex items-center gap-1">
                              <Eye className="h-4 w-4" />
                              {manga.view_count} 浏览
                            </div>
                            <div className="flex items-center gap-1">
                              <Heart className="h-4 w-4" />
                              {manga.favorite_count} 收藏
                            </div>
                            <span>{manga.chapter_count} 章节</span>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {formatDate(manga.updated_at)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex justify-center gap-2 mt-8">
              <Button
                variant="outline"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                上一页
              </Button>
              <span className="flex items-center px-4">
                第 {currentPage} 页，共 {totalPages} 页
              </span>
              <Button
                variant="outline"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                下一页
              </Button>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12">
          <p className="text-lg text-muted-foreground">
            {searchTerm ? '没有找到匹配的漫画' : '暂无漫画'}
          </p>
        </div>
      )}
    </div>
  );
}