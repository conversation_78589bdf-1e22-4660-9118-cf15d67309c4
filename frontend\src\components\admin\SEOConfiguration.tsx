'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { apiClient } from '@/lib/api';
import { 
  Save,
  Globe,
  Search,
  FileText,
  Link,
  AlertCircle,
  CheckCircle,
  Loader2,
  Download,
  RefreshCw
} from 'lucide-react';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from '@/components/ui/select';

interface SEOConfig {
  site_title: string;
  site_description: string;
  site_keywords: string;
  og_title: string;
  og_description: string;
  og_image: string;
  twitter_card: string;
  twitter_site: string;
  robots_txt: string;
  sitemap_frequency: string;
  google_analytics_id: string;
  baidu_analytics_id: string;
  canonical_url: string;
}

interface PageSEO {
  path: string;
  title: string;
  description: string;
  keywords: string;
  priority: number;
  changefreq: string;
}

export default function SEOConfiguration() {
  const [seoConfig, setSeoConfig] = useState<SEOConfig>({
    site_title: '',
    site_description: '',
    site_keywords: '',
    og_title: '',
    og_description: '',
    og_image: '',
    twitter_card: 'summary_large_image',
    twitter_site: '',
    robots_txt: '',
    sitemap_frequency: 'weekly',
    google_analytics_id: '',
    baidu_analytics_id: '',
    canonical_url: ''
  });

  const [pageSEOs, setPageSEOs] = useState<PageSEO[]>([
    { path: '/', title: '首页', description: '', keywords: '', priority: 1.0, changefreq: 'daily' },
    { path: '/anime', title: '动漫', description: '', keywords: '', priority: 0.9, changefreq: 'daily' },
    { path: '/manga', title: '漫画', description: '', keywords: '', priority: 0.9, changefreq: 'daily' },
    { path: '/rifan', title: '里番', description: '', keywords: '', priority: 0.8, changefreq: 'weekly' }
  ]);

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [generateSitemapLoading, setGenerateSitemapLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('general');

  useEffect(() => {
    fetchSEOConfig();
    fetchPageSEOs();
  }, []);

  const fetchSEOConfig = async () => {
    try {
      setLoading(true);
      const config = await apiClient.getSEOConfig();
      setSeoConfig(config);
    } catch (error) {
      console.error('Failed to fetch SEO config:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchPageSEOs = async () => {
    try {
      const seos = await apiClient.getPageSEOs();
      setPageSEOs(seos);
    } catch (error) {
      console.error('Failed to fetch page SEOs:', error);
    }
  };

  const handleSaveConfig = async () => {
    setSaving(true);
    try {
      await apiClient.updateSEOConfig(seoConfig);
      alert('SEO配置已保存');
    } catch (error) {
      console.error('Failed to save SEO config:', error);
      alert('保存失败');
    } finally {
      setSaving(false);
    }
  };

  const handleSavePageSEO = async (pageSEO: PageSEO) => {
    try {
      await apiClient.updatePageSEO(pageSEO);
      alert(`页面 ${pageSEO.path} 的SEO配置已保存`);
      fetchPageSEOs();
    } catch (error) {
      console.error('Failed to save page SEO:', error);
    }
  };

  const handleGenerateSitemap = async () => {
    setGenerateSitemapLoading(true);
    try {
      const result = await apiClient.generateSitemap();
      alert(`Sitemap生成成功！包含 ${result.urls_count} 个URL`);
    } catch (error) {
      console.error('Failed to generate sitemap:', error);
      alert('Sitemap生成失败');
    } finally {
      setGenerateSitemapLoading(false);
    }
  };

  const handleDownloadSitemap = () => {
    window.open('/sitemap.xml', '_blank');
  };

  const handleUpdatePageSEO = (index: number, field: keyof PageSEO, value: any) => {
    const updated = [...pageSEOs];
    updated[index] = { ...updated[index], [field]: value };
    setPageSEOs(updated);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Search className="w-5 h-5" />
          SEO配置管理
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 w-full">
            <TabsTrigger value="general">基础设置</TabsTrigger>
            <TabsTrigger value="social">社交媒体</TabsTrigger>
            <TabsTrigger value="pages">页面SEO</TabsTrigger>
            <TabsTrigger value="sitemap">Sitemap</TabsTrigger>
          </TabsList>

          {/* General SEO Settings */}
          <TabsContent value="general" className="space-y-4">
            <div>
              <Label htmlFor="site_title">网站标题</Label>
              <Input
                id="site_title"
                value={seoConfig.site_title}
                onChange={(e) => setSeoConfig({...seoConfig, site_title: e.target.value})}
                placeholder="网站名称 - 您的品牌标语"
              />
              <p className="text-sm text-muted-foreground mt-1">
                建议长度：50-60个字符
              </p>
            </div>

            <div>
              <Label htmlFor="site_description">网站描述</Label>
              <Textarea
                id="site_description"
                value={seoConfig.site_description}
                onChange={(e) => setSeoConfig({...seoConfig, site_description: e.target.value})}
                rows={3}
                placeholder="网站的简要描述，将显示在搜索结果中"
              />
              <p className="text-sm text-muted-foreground mt-1">
                建议长度：150-160个字符
              </p>
            </div>

            <div>
              <Label htmlFor="site_keywords">关键词</Label>
              <Textarea
                id="site_keywords"
                value={seoConfig.site_keywords}
                onChange={(e) => setSeoConfig({...seoConfig, site_keywords: e.target.value})}
                rows={2}
                placeholder="关键词1, 关键词2, 关键词3"
              />
              <p className="text-sm text-muted-foreground mt-1">
                用逗号分隔，建议5-10个核心关键词
              </p>
            </div>

            <div>
              <Label htmlFor="canonical_url">规范URL</Label>
              <Input
                id="canonical_url"
                value={seoConfig.canonical_url}
                onChange={(e) => setSeoConfig({...seoConfig, canonical_url: e.target.value})}
                placeholder="https://yourdomain.com"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="google_analytics_id">Google Analytics ID</Label>
                <Input
                  id="google_analytics_id"
                  value={seoConfig.google_analytics_id}
                  onChange={(e) => setSeoConfig({...seoConfig, google_analytics_id: e.target.value})}
                  placeholder="G-XXXXXXXXXX"
                />
              </div>
              <div>
                <Label htmlFor="baidu_analytics_id">百度统计ID</Label>
                <Input
                  id="baidu_analytics_id"
                  value={seoConfig.baidu_analytics_id}
                  onChange={(e) => setSeoConfig({...seoConfig, baidu_analytics_id: e.target.value})}
                  placeholder="百度统计代码"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="robots_txt">robots.txt内容</Label>
              <Textarea
                id="robots_txt"
                value={seoConfig.robots_txt}
                onChange={(e) => setSeoConfig({...seoConfig, robots_txt: e.target.value})}
                rows={6}
                placeholder={`User-agent: *
Allow: /
Disallow: /admin/
Sitemap: https://yourdomain.com/sitemap.xml`}
                className="font-mono"
              />
            </div>
          </TabsContent>

          {/* Social Media SEO */}
          <TabsContent value="social" className="space-y-4">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Open Graph设置</h3>
              
              <div>
                <Label htmlFor="og_title">OG标题</Label>
                <Input
                  id="og_title"
                  value={seoConfig.og_title}
                  onChange={(e) => setSeoConfig({...seoConfig, og_title: e.target.value})}
                  placeholder="社交媒体分享时显示的标题"
                />
              </div>

              <div>
                <Label htmlFor="og_description">OG描述</Label>
                <Textarea
                  id="og_description"
                  value={seoConfig.og_description}
                  onChange={(e) => setSeoConfig({...seoConfig, og_description: e.target.value})}
                  rows={3}
                  placeholder="社交媒体分享时显示的描述"
                />
              </div>

              <div>
                <Label htmlFor="og_image">OG图片</Label>
                <Input
                  id="og_image"
                  value={seoConfig.og_image}
                  onChange={(e) => setSeoConfig({...seoConfig, og_image: e.target.value})}
                  placeholder="https://yourdomain.com/og-image.jpg"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  建议尺寸：1200 x 630 像素
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Twitter Card设置</h3>
              
              <div>
                <Label htmlFor="twitter_card">Card类型</Label>
                <Select 
                  value={seoConfig.twitter_card}
                  onValueChange={(v) => setSeoConfig({...seoConfig, twitter_card: v})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="summary">Summary</SelectItem>
                    <SelectItem value="summary_large_image">Summary Large Image</SelectItem>
                    <SelectItem value="app">App</SelectItem>
                    <SelectItem value="player">Player</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="twitter_site">Twitter账号</Label>
                <Input
                  id="twitter_site"
                  value={seoConfig.twitter_site}
                  onChange={(e) => setSeoConfig({...seoConfig, twitter_site: e.target.value})}
                  placeholder="@yourusername"
                />
              </div>
            </div>
          </TabsContent>

          {/* Page-specific SEO */}
          <TabsContent value="pages" className="space-y-4">
            <div className="space-y-4">
              {pageSEOs.map((pageSEO, index) => (
                <Card key={pageSEO.path}>
                  <CardHeader>
                    <CardTitle className="text-base flex items-center gap-2">
                      <FileText className="w-4 h-4" />
                      {pageSEO.title} ({pageSEO.path})
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <Label>页面标题</Label>
                      <Input
                        value={pageSEO.title}
                        onChange={(e) => handleUpdatePageSEO(index, 'title', e.target.value)}
                      />
                    </div>
                    <div>
                      <Label>页面描述</Label>
                      <Textarea
                        value={pageSEO.description}
                        onChange={(e) => handleUpdatePageSEO(index, 'description', e.target.value)}
                        rows={2}
                      />
                    </div>
                    <div>
                      <Label>页面关键词</Label>
                      <Input
                        value={pageSEO.keywords}
                        onChange={(e) => handleUpdatePageSEO(index, 'keywords', e.target.value)}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>优先级 (0.0-1.0)</Label>
                        <Input
                          type="number"
                          min="0"
                          max="1"
                          step="0.1"
                          value={pageSEO.priority}
                          onChange={(e) => handleUpdatePageSEO(index, 'priority', parseFloat(e.target.value))}
                        />
                      </div>
                      <div>
                        <Label>更新频率</Label>
                        <Select 
                          value={pageSEO.changefreq}
                          onValueChange={(v) => handleUpdatePageSEO(index, 'changefreq', v)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="always">总是</SelectItem>
                            <SelectItem value="hourly">每小时</SelectItem>
                            <SelectItem value="daily">每天</SelectItem>
                            <SelectItem value="weekly">每周</SelectItem>
                            <SelectItem value="monthly">每月</SelectItem>
                            <SelectItem value="yearly">每年</SelectItem>
                            <SelectItem value="never">从不</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <Button 
                      onClick={() => handleSavePageSEO(pageSEO)}
                      size="sm"
                      className="w-full"
                    >
                      保存页面SEO
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Sitemap Settings */}
          <TabsContent value="sitemap" className="space-y-4">
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Sitemap设置</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="sitemap_frequency">默认更新频率</Label>
                    <Select 
                      value={seoConfig.sitemap_frequency}
                      onValueChange={(v) => setSeoConfig({...seoConfig, sitemap_frequency: v})}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="always">总是</SelectItem>
                        <SelectItem value="hourly">每小时</SelectItem>
                        <SelectItem value="daily">每天</SelectItem>
                        <SelectItem value="weekly">每周</SelectItem>
                        <SelectItem value="monthly">每月</SelectItem>
                        <SelectItem value="yearly">每年</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex gap-2">
                    <Button 
                      onClick={handleGenerateSitemap}
                      disabled={generateSitemapLoading}
                      className="flex items-center gap-2"
                    >
                      {generateSitemapLoading ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <RefreshCw className="w-4 h-4" />
                      )}
                      生成Sitemap
                    </Button>
                    <Button 
                      onClick={handleDownloadSitemap}
                      variant="outline"
                      className="flex items-center gap-2"
                    >
                      <Download className="w-4 h-4" />
                      下载Sitemap
                    </Button>
                  </div>

                  <div className="bg-secondary p-3 rounded-lg">
                    <div className="flex items-start gap-2">
                      <AlertCircle className="w-4 h-4 text-orange-500 mt-0.5" />
                      <div className="text-sm">
                        <p>Sitemap将包含所有公开的页面，包括：</p>
                        <ul className="list-disc list-inside mt-1 text-muted-foreground">
                          <li>首页和分类页</li>
                          <li>所有已发布的动漫页面</li>
                          <li>所有已发布的漫画页面</li>
                          <li>其他公开页面</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Save Button */}
        <div className="flex justify-end mt-6">
          <Button 
            onClick={handleSaveConfig}
            disabled={saving || loading}
            className="flex items-center gap-2"
          >
            {saving ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Save className="w-4 h-4" />
            )}
            {saving ? '保存中...' : '保存SEO配置'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}