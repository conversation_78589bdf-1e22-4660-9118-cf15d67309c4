'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  Menu,
  Settings,
  ChevronLeft,
  ChevronRight,
  X,
  Home
} from 'lucide-react';

export interface MobileFABProps {
  onSettingsOpen: () => void;
  onPrevChapter?: () => void;
  onNextChapter?: () => void;
  onBackToManga?: () => void;
  hasNextChapter?: boolean;
  hasPrevChapter?: boolean;
  className?: string;
}

export function MobileFAB({
  onSettingsOpen,
  onPrevChapter,
  onNextChapter,
  onBackToManga,
  hasNextChapter = false,
  hasPrevChapter = false,
  className
}: MobileFABProps) {
  const [expanded, setExpanded] = useState(false);

  const handleMainAction = () => {
    if (expanded) {
      setExpanded(false);
    } else {
      setExpanded(true);
    }
  };

  const handleActionClick = (action: () => void) => {
    action();
    setExpanded(false);
  };

  const actions = [
    {
      key: 'settings',
      icon: Settings,
      label: '设置',
      action: onSettingsOpen,
      show: true
    },
    {
      key: 'home',
      icon: Home,
      label: '返回漫画',
      action: onBackToManga,
      show: !!onBackToManga
    },
    {
      key: 'prev',
      icon: ChevronLeft,
      label: '上一章',
      action: onPrevChapter,
      show: hasPrevChapter && !!onPrevChapter
    },
    {
      key: 'next',
      icon: ChevronRight,
      label: '下一章',
      action: onNextChapter,
      show: hasNextChapter && !!onNextChapter
    }
  ].filter(action => action.show);

  return (
    <>
      {/* Backdrop overlay when expanded */}
      {expanded && (
        <div
          className="fixed inset-0 bg-transparent z-30"
          onClick={() => setExpanded(false)}
        />
      )}

      <div
        className={cn(
          "fixed z-40 flex flex-col-reverse items-end gap-3",
          // Position with safe area support
          "bottom-[max(1.5rem,calc(env(safe-area-inset-bottom)+1.5rem))]",
          "right-6",
          className
        )}
      >
        {/* Action buttons - only show when expanded */}
        {expanded && (
          <div className="flex flex-col-reverse gap-3 animate-in slide-in-from-bottom-2 duration-200">
            {actions.map((action, index) => {
              const Icon = action.icon;
              return (
                <Button
                  key={action.key}
                  size="icon"
                  variant="secondary"
                  className={cn(
                    "mobile-touch-button h-12 w-12 rounded-full",
                    "bg-background/90 backdrop-blur-sm border",
                    "shadow-lg hover:shadow-xl transition-all duration-200",
                    "animate-in slide-in-from-bottom-1 duration-150"
                  )}
                  style={{
                    animationDelay: `${index * 50}ms`
                  }}
                  onClick={() => handleActionClick(action.action)}
                >
                  <Icon className="h-5 w-5" />
                  <span className="sr-only">{action.label}</span>
                </Button>
              );
            })}
          </div>
        )}

        {/* Main FAB button */}
        <Button
          size="icon"
          className={cn(
            "mobile-touch-button h-14 w-14 rounded-full",
            "bg-primary text-primary-foreground",
            "shadow-lg hover:shadow-xl transition-all duration-200",
            "manga-float-shadow",
            expanded && "rotate-45"
          )}
          onClick={handleMainAction}
        >
          {expanded ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          <span className="sr-only">
            {expanded ? '关闭菜单' : '打开菜单'}
          </span>
        </Button>
      </div>
    </>
  );
}