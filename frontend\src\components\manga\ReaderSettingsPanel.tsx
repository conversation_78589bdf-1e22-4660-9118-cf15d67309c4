'use client';

import { SlideOutPanel } from '@/components/ui/slide-out-panel';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import {
  Palette,
  Type,
  Moon,
  Sun,
  ScrollText,
  Layers,
  ZoomIn,
  ZoomOut,
  BookOpen,
  Sparkles,
  Eye,
  Leaf,
  Coffee,
  Sunset
} from 'lucide-react';

interface ReaderSettingsPanelProps {
  isOpen: boolean;
  onClose: () => void;
  theme: 'default' | 'dark' | 'parchment' | 'paper' | 'sepia' | 'green' | 'blue';
  onThemeChange: (theme: 'default' | 'dark' | 'parchment' | 'paper' | 'sepia' | 'green' | 'blue') => void;
  readingMode: 'flip' | 'scroll';
  onReadingModeChange: (mode: 'flip' | 'scroll') => void;
  showFocusMask: boolean;
  onFocusMaskChange: (show: boolean) => void;
  continuousReading?: boolean;
  onContinuousReadingChange?: (enabled: boolean) => void;
  fontSize?: number;
  onFontSizeChange?: (size: number) => void;
  zoom: number;
  onZoomChange: (zoom: number) => void;
  onFitToWidth: () => void;
  onFitToHeight: () => void;
  onResetZoom: () => void;
}

export function ReaderSettingsPanel({
  isOpen,
  onClose,
  theme,
  onThemeChange,
  readingMode,
  onReadingModeChange,
  showFocusMask,
  onFocusMaskChange,
  continuousReading = true,
  onContinuousReadingChange,
  fontSize = 16,
  onFontSizeChange,
  zoom,
  onZoomChange,
  onFitToWidth,
  onFitToHeight,
  onResetZoom
}: ReaderSettingsPanelProps) {
  const themes = [
    { id: 'default', name: '默认', icon: Sun, color: 'bg-white', description: '明亮清晰' },
    { id: 'dark', name: '夜间', icon: Moon, color: 'bg-gray-900', description: '深色护眼' },
    { id: 'sepia', name: '护眼', icon: Eye, color: 'bg-amber-100', description: '温暖柔和' },
    { id: 'green', name: '护眼绿', icon: Leaf, color: 'bg-green-100', description: '清新自然' },
    { id: 'parchment', name: '羊皮卷', icon: BookOpen, color: 'bg-amber-50', description: '复古典雅' },
    { id: 'paper', name: '纸感', icon: Sparkles, color: 'bg-stone-50', description: '简约舒适' },
    { id: 'blue', name: '蓝光护眼', icon: Sunset, color: 'bg-blue-50', description: '减少蓝光' }
  ];

  return (
    <SlideOutPanel
      isOpen={isOpen}
      onClose={onClose}
      title="阅读设置"
      position="right"
      className="w-80"
    >
      <div className="p-4 space-y-6">
        {/* 主题选择 */}
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-sm font-medium">
            <Palette className="h-4 w-4" />
            <span>阅读主题</span>
          </div>
          <div className="grid grid-cols-1 gap-2">
            {themes.map((t) => {
              const Icon = t.icon;
              return (
                <Button
                  key={t.id}
                  variant={theme === t.id ? 'default' : 'outline'}
                  className="flex items-center gap-3 h-auto py-3 px-4 justify-start"
                  onClick={() => onThemeChange(t.id as any)}
                >
                  <div className={`w-6 h-6 rounded-full ${t.color} border shadow-sm`} />
                  <Icon className="h-4 w-4" />
                  <div className="flex-1 text-left">
                    <div className="text-sm font-medium">{t.name}</div>
                    <div className="text-xs text-muted-foreground">{t.description}</div>
                  </div>
                  {theme === t.id && (
                    <div className="w-2 h-2 bg-primary rounded-full" />
                  )}
                </Button>
              );
            })}
          </div>
        </div>

        <Separator />

        {/* 阅读模式 */}
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-sm font-medium">
            <BookOpen className="h-4 w-4" />
            <span>阅读模式</span>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant={readingMode === 'flip' ? 'default' : 'outline'}
              onClick={() => onReadingModeChange('flip')}
              className="flex items-center gap-2"
            >
              <Layers className="h-4 w-4" />
              <span>翻页模式</span>
            </Button>
            <Button
              variant={readingMode === 'scroll' ? 'default' : 'outline'}
              onClick={() => onReadingModeChange('scroll')}
              className="flex items-center gap-2"
            >
              <ScrollText className="h-4 w-4" />
              <span>滚动模式</span>
            </Button>
          </div>
        </div>

        <Separator />

        {/* 缩放控制 */}
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-sm font-medium">
            <ZoomIn className="h-4 w-4" />
            <span>缩放控制</span>
          </div>
          
          <div className="space-y-3">
            {/* 缩放按钮 */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onZoomChange(Math.max(0.5, zoom - 0.25))}
                className="flex-1"
              >
                <ZoomOut className="h-4 w-4 mr-1" />
                缩小
              </Button>
              <div className="text-center flex-1">
                <span className="font-mono text-sm font-medium">{Math.round(zoom * 100)}%</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onZoomChange(Math.min(3, zoom + 0.25))}
                className="flex-1"
              >
                <ZoomIn className="h-4 w-4 mr-1" />
                放大
              </Button>
            </div>
            
            {/* 快速设置 */}
            <div className="grid grid-cols-3 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onFitToWidth}
                className="text-xs"
              >
                适配宽度
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={onFitToHeight}
                className="text-xs"
              >
                适配高度
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={onResetZoom}
                className="text-xs"
              >
                原始大小
              </Button>
            </div>
          </div>
        </div>

        <Separator />

        {/* 阅读体验 */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-sm font-medium">
            <Sparkles className="h-4 w-4" />
            <span>阅读体验</span>
          </div>

          {/* 连续阅读 */}
          {readingMode === 'scroll' && onContinuousReadingChange && (
            <div className="flex items-center justify-between">
              <Label htmlFor="continuous-reading" className="text-sm cursor-pointer">
                连续阅读
                <p className="text-xs text-muted-foreground mt-1">
                  滚动到底部自动加载下一章
                </p>
              </Label>
              <Switch
                id="continuous-reading"
                checked={continuousReading}
                onCheckedChange={onContinuousReadingChange}
              />
            </div>
          )}

          {/* 聚焦遮罩 */}
          <div className="flex items-center justify-between">
            <Label htmlFor="focus-mask" className="text-sm cursor-pointer">
              聚焦遮罩
              <p className="text-xs text-muted-foreground mt-1">
                顶部和底部添加半透明遮罩
              </p>
            </Label>
            <Switch
              id="focus-mask"
              checked={showFocusMask}
              onCheckedChange={onFocusMaskChange}
            />
          </div>

          {/* 字体大小（如果需要） */}
          {onFontSizeChange && (
            <div className="space-y-2">
              <Label className="text-sm">字体大小</Label>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onFontSizeChange(Math.max(12, fontSize - 2))}
                >
                  <Type className="h-3 w-3" />
                  <span className="text-xs">-</span>
                </Button>
                <span className="text-sm font-mono flex-1 text-center">
                  {fontSize}px
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onFontSizeChange(Math.min(24, fontSize + 2))}
                >
                  <Type className="h-4 w-4" />
                  <span className="text-xs">+</span>
                </Button>
              </div>
            </div>
          )}
        </div>

        <Separator />

        {/* 快捷键说明 */}
        <div className="text-xs text-muted-foreground space-y-3">
          <div>
            <p className="font-medium mb-2">⌨️ 快捷键：</p>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <p className="font-medium text-foreground">翻页：</p>
                <p>← / A：上一页</p>
                <p>→ / D / 空格：下一页</p>
                <p>Home：第一页</p>
                <p>End：最后一页</p>
              </div>
              <div>
                <p className="font-medium text-foreground">缩放：</p>
                <p>+ / =：放大</p>
                <p>-：缩小</p>
                <p>0：重置</p>
                <p>F：全屏</p>
                <p>Esc：退出</p>
              </div>
            </div>
          </div>

          <div>
            <p className="font-medium mb-2">📱 手势：</p>
            <ul className="space-y-1 pl-2">
              <li>• 左右滑动：翻页（翻页模式）</li>
              <li>• 上下滑动：滚动（滚动模式）</li>
              <li>• 点击空白：显示/隐藏工具栏</li>
              <li>• 双击图片：快速缩放</li>
            </ul>
          </div>

          <div>
            <p className="font-medium mb-2">💡 提示：</p>
            <ul className="space-y-1 pl-2">
              <li>• 护眼主题适合长时间阅读</li>
              <li>• 连续阅读可无缝切换章节</li>
              <li>• 聚焦遮罩减少干扰</li>
            </ul>
          </div>
        </div>
      </div>
    </SlideOutPanel>
  );
}