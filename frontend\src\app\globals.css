@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

/* Toast animations */
@keyframes slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  
  html {
    overflow-x: hidden;
    /* 确保中文字体正确渲染 */
    font-feature-settings: "kern";
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  body {
    @apply bg-background text-foreground;
    /* 移动端背景固定优化 */
    position: relative;
    overflow-x: hidden;
    /* 防止移动端文字大小自动调整 */
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
    /* 优化中文字体渲染 */
    font-variant-ligatures: normal;
    font-variant-numeric: normal;
    /* 防止文字换行问题 */
    word-break: normal;
    word-wrap: break-word;
    line-height: 1.5;
  }
  
  /* 移动端特定优化 */
  @media (max-width: 768px) {
    html, body {
      height: 100%;
      overflow-x: hidden;
      position: relative;
    }
    
    /* 防止移动端背景滚动 */
    body {
      background-attachment: fixed;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center center;
      /* 防止iOS Safari的bounce效果 */
      overscroll-behavior: none;
      -webkit-overflow-scrolling: touch;
    }
    
    /* 防止内容超出视口边界 */
    .container {
      max-width: 100%;
      overflow-x: hidden;
    }
    
    /* 修复移动端触摸滚动问题 */
    * {
      -webkit-overflow-scrolling: touch;
      /* 防止iOS Safari的拉伸效果 */
      overscroll-behavior: contain;
    }
    
    /* 防止水平滚动 */
    main {
      overflow-x: hidden;
      width: 100%;
      position: relative;
    }
    
    /* 固定背景在移动端的特殊处理 */
    html {
      height: 100%;
      overflow-x: hidden;
      /* 防止iOS Safari的滚动回弹 */
      -webkit-overflow-scrolling: touch;
      overscroll-behavior: none;
    }
    
    /* 防止页面整体被拖拽 */
    #__next {
      overflow-x: hidden;
      width: 100%;
      min-height: 100vh;
    }
  }
  
  /* 确保全屏容器不会超出边界 */
  .min-h-screen {
    min-height: 100vh;
    overflow-x: hidden;
  }
  
  /* 全局防止水平滚动 */
  html {
    overflow-x: hidden;
  }
  
  /* 隐藏滚动条样式 */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }
  
  /* 漫画阅读器自定义滚动条 */
  .manga-reader-scroll::-webkit-scrollbar {
    width: 8px;
  }
  
  .manga-reader-scroll::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .manga-reader-scroll::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    transition: background-color 0.3s ease;
  }
  
  .manga-reader-scroll::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.4);
  }
  
  .dark .manga-reader-scroll::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
  }
  
  .dark .manga-reader-scroll::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.4);
  }
  
  /* Firefox 滚动条 */
  .manga-reader-scroll {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
  }
  
  .dark .manga-reader-scroll {
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
  }
  
  /* 移动端触摸滑动优化 */
  .touch-pan-x {
    touch-action: pan-x;
  }
  
  .smooth-scroll {
    scroll-behavior: smooth;
  }
  
  /* 中文文字优化 */
  h1, h2, h3, h4, h5, h6, p, span, div {
    /* 防止中文字符被错误换行 */
    word-break: keep-all;
    overflow-wrap: break-word;
    /* 优化中文字体间距 */
    letter-spacing: 0.05em;
  }
  
  /* 移动端中文文字特别优化 */
  @media (max-width: 768px) {
    h1, h2, h3, h4, h5, h6 {
      /* 确保标题在移动端正确显示 */
      line-height: 1.4;
      word-break: keep-all;
      overflow-wrap: break-word;
      hyphens: none;
      -webkit-hyphens: none;
      -moz-hyphens: none;
      -ms-hyphens: none;
    }
    
    p, span, div {
      /* 优化段落文字在移动端的显示 */
      line-height: 1.6;
      word-break: keep-all;
      overflow-wrap: break-word;
    }
    
    /* 防止移动端Safari的字体自动放大 */
    input, textarea, select {
      font-size: 16px;
      -webkit-text-size-adjust: 100%;
    }
  }

  /* Line Clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* 漫画阅读器特定样式 */
  .manga-reader {
    /* 防止页面滚动 */
    height: 100vh;
    overflow: hidden;
    position: relative;
  }

  .manga-page-img {
    /* 图片选择和拖拽禁用 */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    pointer-events: none;
    /* 图片显示优化 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: optimize-contrast;
    /* GPU加速 */
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* 触摸手势优化 */
  .touch-gesture-area {
    touch-action: pan-x pan-y;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }

  /* 页面切换动画 */
  .page-transition-fade {
    transition: opacity 0.2s ease-out;
  }

  .page-transition-slide {
    transition: transform 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
  }

  /* 移动端按钮优化 */
  @media (max-width: 768px) {
    .mobile-touch-button {
      min-height: 44px;
      min-width: 44px;
      padding: 12px;
      touch-action: manipulation;
    }
  }

  /* 夜间模式图片滤镜 */
  .dark .manga-page-img {
    filter: brightness(0.9) contrast(1.1);
  }

  /* iOS Safari 特定优化 */
  @supports (-webkit-touch-callout: none) {
    .ios-safe-area-top {
      padding-top: max(1rem, env(safe-area-inset-top));
    }
    
    .ios-safe-area-bottom {
      padding-bottom: max(1rem, env(safe-area-inset-bottom));
    }

    /* 防止iOS Safari的橡皮筋效果 */
    .prevent-bounce {
      overscroll-behavior: none;
      -webkit-overflow-scrolling: touch;
    }
  }

  /* 性能优化类 */
  .gpu-layer {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  .will-change-transform {
    will-change: transform;
  }

  .will-change-opacity {
    will-change: opacity;
  }

  /* 新增的现代化动画效果 */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out forwards;
  }
  
  /* 漫画阅读器页面过渡 */
  .manga-page-transition {
    animation: pageTransition 0.25s cubic-bezier(0.22, 1, 0.36, 1) forwards;
  }
  
  @keyframes pageTransition {
    from { 
      opacity: 0.7;
      transform: translateX(20px);
    }
    to { 
      opacity: 1;
      transform: translateX(0);
    }
  }

  .animate-slide-up {
    animation: slideUp 0.4s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
  }

  .animate-slide-down {
    animation: slideDown 0.4s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out forwards;
  }

  /* 高级毛玻璃效果 */
  .glass-morphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(16px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .dark .glass-morphism {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }
  
  /* 漫画阅读器浮层阴影 */
  .manga-float-shadow {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1), 0 0 1px rgba(0, 0, 0, 0.1);
  }
  
  .dark .manga-float-shadow {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4), 0 0 1px rgba(255, 255, 255, 0.1);
  }
  
  /* 纸感主题 */
  .paper-theme, .reader-paper {
    background: #fdfcf8;
    color: #2d2d2d;
  }
  
  /* 阅读器全屏优化 */
  body:has(.manga-reader-container) {
    overflow: hidden;
  }
  
  .paper-theme .manga-page-img, .reader-paper .manga-page-img {
    filter: sepia(0.05) brightness(0.98) contrast(1.05);
  }
  
  /* 羊皮卷主题 */
  .reader-parchment {
    position: relative;
    background: 
      linear-gradient(135deg, #f4e4bc 0%, #e8d4a0 25%, #f2dec4 50%, #e8d4a0 75%, #f4e4bc 100%),
      radial-gradient(ellipse at top, rgba(139, 87, 42, 0.1) 0%, transparent 70%),
      radial-gradient(ellipse at bottom, rgba(139, 87, 42, 0.1) 0%, transparent 70%);
    color: #4a3426;
  }
  
  .reader-parchment::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      repeating-linear-gradient(
        90deg,
        transparent,
        transparent 50px,
        rgba(139, 87, 42, 0.03) 50px,
        rgba(139, 87, 42, 0.03) 51px
      ),
      repeating-linear-gradient(
        0deg,
        transparent,
        transparent 50px,
        rgba(139, 87, 42, 0.03) 50px,
        rgba(139, 87, 42, 0.03) 51px
      );
    pointer-events: none;
    z-index: 1;
  }
  
  .reader-parchment .manga-page-img {
    filter: sepia(0.2) brightness(0.95) contrast(1.1);
    box-shadow: 0 0 30px rgba(139, 87, 42, 0.2);
  }
  
  .reader-parchment .bg-background,
  .reader-parchment .bg-background\/90,
  .reader-parchment .bg-background\/95 {
    background-color: rgba(244, 228, 188, 0.9) !important;
  }
  
  .reader-parchment .text-foreground {
    color: #4a3426 !important;
  }
  
  .reader-parchment .text-muted-foreground {
    color: #6b5440 !important;
  }
  
  .reader-parchment .border,
  .reader-parchment .border-t,
  .reader-parchment .border-b {
    border-color: rgba(139, 87, 42, 0.2) !important;
  }
  
  /* 阅读器暗色主题 */
  .reader-dark .manga-page-img {
    filter: brightness(0.85) contrast(1.1);
  }
  
  .reader-dark .bg-background,
  .reader-dark .bg-background\/90,
  .reader-dark .bg-background\/95 {
    background-color: rgba(17, 24, 39, 0.9) !important;
  }
  
  /* 护眼模式 */
  .eye-protection {
    background: #f5f2e8;
    color: #3a3a3a;
  }
  
  .eye-protection .manga-page-img {
    filter: sepia(0.1) brightness(0.95) contrast(0.95);
  }
  
  /* 漫画页面周边淡投影 */
  .manga-page-shadow {
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.05);
  }
  
  .dark .manga-page-shadow {
    box-shadow: 0 0 40px rgba(255, 255, 255, 0.02);
  }

  /* 现代化进度条样式 */
  .modern-progress {
    background: linear-gradient(90deg, 
      transparent 0%, 
      rgba(var(--primary), 0.3) 25%, 
      rgba(var(--primary), 0.6) 50%, 
      rgba(var(--primary), 0.3) 75%, 
      transparent 100%
    );
    animation: progressShimmer 2s ease-in-out infinite;
  }

  /* 沉浸式阅读优化 */
  .immersive-reading {
    background: linear-gradient(
      180deg, 
      transparent 0%, 
      rgba(0, 0, 0, 0.02) 50%, 
      transparent 100%
    );
  }

  .dark .immersive-reading {
    background: linear-gradient(
      180deg, 
      transparent 0%, 
      rgba(255, 255, 255, 0.01) 50%, 
      transparent 100%
    );
  }
  
  /* 阅读遮罩 - 聚焦条 */
  .reading-focus-mask {
    pointer-events: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0.14) 0%,
      transparent 14%,
      transparent 86%,
      rgba(0, 0, 0, 0.14) 100%
    );
    z-index: 20;
  }
  
  .dark .reading-focus-mask,
  .reader-dark .reading-focus-mask {
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0.3) 0%,
      transparent 14%,
      transparent 86%,
      rgba(0, 0, 0, 0.3) 100%
    );
  }
  
  .reader-parchment .reading-focus-mask {
    background: linear-gradient(
      180deg,
      rgba(74, 52, 38, 0.2) 0%,
      transparent 14%,
      transparent 86%,
      rgba(74, 52, 38, 0.2) 100%
    );
  }

  /* 关键帧动画 */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes slideUp {
    from { transform: translateY(100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  @keyframes slideDown {
    from { transform: translateY(-100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  @keyframes scaleIn {
    from { transform: scale(0.95); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
  }

  @keyframes progressShimmer {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
  }

  /* 触摸优化 */
  .touch-optimized {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  /* 文字选择优化 */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}
