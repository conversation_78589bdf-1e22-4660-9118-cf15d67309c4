'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { apiClient, User } from '@/lib/api';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (username: string, password: string) => Promise<void>;
  register: (username: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [hasToken, setHasToken] = useState(false);

  useEffect(() => {
    const initAuth = async () => {
      const token = localStorage.getItem('access_token');
      setHasToken(!!token);
      
      if (token && apiClient.isAuthenticated()) {
        try {
          const userData = await apiClient.getCurrentUser();
          setUser(userData);
        } catch (error) {
          console.error('Failed to fetch user data:', error);
          // 只有在明确的401认证错误时才logout，其他错误保持登录状态
          if (error instanceof Error) {
            const errorMessage = error.message.toLowerCase();
            const statusMatch = error.message.match(/status:\s*(\d+)/i);
            const status = statusMatch ? parseInt(statusMatch[1]) : null;
            
            if (status === 401 || 
                errorMessage.includes('401') || 
                errorMessage.includes('unauthorized')) {
              console.log('Authentication error (401) detected, logging out');
              apiClient.logout();
              setUser(null);
              setHasToken(false);
            } else {
              // 网络错误或其他5xx错误，保持登录状态
              console.log('Network or server error, keeping login state');
              // 设置一个临时用户信息避免显示登录表单
              const tempUser = {
                id: 0,
                username: localStorage.getItem('username') || 'Unknown',
                email: '',
                is_admin: false,
                avatar_url: null,
                created_at: '',
                updated_at: ''
              };
              setUser(tempUser);
            }
          }
        }
      }
      setLoading(false);
    };

    initAuth();
  }, []);

  const login = async (username: string, password: string) => {
    setLoading(true);
    try {
      const loginResponse = await apiClient.login({ username, password });
      setUser(loginResponse.user); // Use user data from login response
      setHasToken(true);
      // 存储用户名用于临时显示
      localStorage.setItem('username', loginResponse.user.username);
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (username: string, email: string, password: string) => {
    setLoading(true);
    try {
      await apiClient.register({ username, email, password });
      // Auto-login after registration
      await login(username, password);
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    apiClient.logout();
    setUser(null);
    setHasToken(false);
    // 清理存储的用户名
    localStorage.removeItem('username');
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    register,
    logout,
    isAuthenticated: !!user || hasToken,
    isAdmin: user?.is_admin ?? false,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};