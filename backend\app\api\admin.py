from typing import List, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.deps import get_current_active_user
from app.models import User as UserModel, SystemConfig, FeaturedAnime, Anime
from app.core.config_manager import Config<PERSON>anager, ConfigType, ConfigCategory
from app.services.email_service import EmailService
from pydantic import BaseModel, EmailStr

router = APIRouter()

# 系统配置相关Schema
class SystemConfigUpdate(BaseModel):
    key: str
    value: str

class SystemConfigResponse(BaseModel):
    key: str
    value: str
    description: str

    class Config:
        from_attributes = True

class ConfigItem(BaseModel):
    value: str
    description: str
    type: str
    category: str
    required: bool
    sensitive: bool = False

class ConfigGroupResponse(BaseModel):
    configs: Dict[str, ConfigItem]

class ConfigBatchUpdate(BaseModel):
    configs: Dict[str, str]

class SMTPTestRequest(BaseModel):
    host: str
    port: int
    use_tls: bool = True
    use_ssl: bool = False
    username: str
    password: str

class TestEmailRequest(BaseModel):
    email: EmailStr
    
class EmailSendResponse(BaseModel):
    success: bool
    message: str
    status_code: int = None

class FeaturedAnimeCreate(BaseModel):
    anime_id: int
    order_index: int = 0
    custom_poster_url: str = None  # 自定义海报URL

class FeaturedAnimeResponse(BaseModel):
    id: int
    anime_id: int
    order_index: int
    custom_poster_url: str = None  # 自定义海报URL
    is_active: bool
    anime: dict  # 改为dict类型以避免循环引用

    class Config:
        from_attributes = True

class FeaturedAnimeUpdate(BaseModel):
    order_index: int = None
    custom_poster_url: str = None
    is_active: bool = None

# 验证管理员权限的依赖
def get_admin_user(current_user: UserModel = Depends(get_current_active_user)):
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user

# 系统配置管理
@router.get("/configs", response_model=List[SystemConfigResponse], summary="获取系统配置")
def get_system_configs(
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    configs = db.query(SystemConfig).all()
    return configs

@router.put("/configs", summary="更新系统配置")
def update_system_configs(
    configs: List[SystemConfigUpdate],
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    try:
        for config_update in configs:
            config = db.query(SystemConfig).filter(SystemConfig.key == config_update.key).first()
            if config:
                config.value = config_update.value
            else:
                # 如果配置不存在，创建新的
                new_config = SystemConfig(
                    key=config_update.key,
                    value=config_update.value,
                    description=f"配置项: {config_update.key}"
                )
                db.add(new_config)
        
        db.commit()
        return {"message": "系统配置更新成功"}
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新配置失败: {str(e)}"
        )

# 新的配置管理端点
@router.get("/configs/grouped", summary="获取按分类分组的配置")
def get_grouped_configs(
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
) -> Dict[str, ConfigGroupResponse]:
    """获取按分类分组的配置"""
    config_manager = ConfigManager(db)
    
    grouped_configs = {}
    for category in ConfigCategory:
        configs = config_manager.get_configs_by_category(category)
        # 隐藏敏感信息
        for key, config in configs.items():
            if config.get("sensitive", False) and config.get("value"):
                config["value"] = "******"
        
        grouped_configs[category.value] = ConfigGroupResponse(configs=configs)
    
    return grouped_configs

@router.get("/configs/category/{category}", summary="获取指定分类的配置")
def get_configs_by_category(
    category: str,
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
) -> ConfigGroupResponse:
    """获取指定分类的配置"""
    try:
        config_category = ConfigCategory(category)
        config_manager = ConfigManager(db)
        configs = config_manager.get_configs_by_category(config_category)
        
        # 隐藏敏感信息
        for key, config in configs.items():
            if config.get("sensitive", False) and config.get("value"):
                config["value"] = "******"
        
        return ConfigGroupResponse(configs=configs)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的配置分类: {category}"
        )

@router.put("/configs/batch", summary="批量更新配置")
def update_configs_batch(
    config_update: ConfigBatchUpdate,
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """批量更新配置"""
    try:
        config_manager = ConfigManager(db)
        updated_configs = config_manager.update_configs_batch(config_update.configs)
        
        return {
            "message": f"成功更新 {len(updated_configs)} 个配置项",
            "updated_count": len(updated_configs)
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量更新配置失败: {str(e)}"
        )

@router.post("/configs/init-defaults", summary="初始化默认配置")
def init_default_configs(
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """初始化默认配置"""
    try:
        config_manager = ConfigManager(db)
        created_configs = config_manager.init_default_configs()
        
        return {
            "message": f"成功初始化 {len(created_configs)} 个默认配置",
            "created_count": len(created_configs)
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"初始化默认配置失败: {str(e)}"
        )

@router.post("/configs/test-smtp", summary="测试SMTP配置")
def test_smtp_config(
    smtp_config: SMTPTestRequest,
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """测试SMTP配置"""
    try:
        # 临时设置SMTP配置用于测试
        temp_configs = {
            "smtp_host": smtp_config.host,
            "smtp_port": str(smtp_config.port),
            "smtp_use_tls": str(smtp_config.use_tls).lower(),
            "smtp_use_ssl": str(smtp_config.use_ssl).lower(),
            "smtp_username": smtp_config.username,
            "smtp_password": smtp_config.password
        }
        
        # 临时更新配置用于测试
        config_manager = ConfigManager(db)
        original_configs = {}
        
        # 保存原始配置
        for key in temp_configs:
            original_configs[key] = config_manager.get_config(key)
        
        # 应用测试配置
        for key, value in temp_configs.items():
            config_manager.set_config(key, value)
        
        # 测试连接
        email_service = EmailService(db)
        result = email_service.test_smtp_connection()
        
        # 恢复原始配置
        for key, value in original_configs.items():
            if value is not None:
                config_manager.set_config(key, value)
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"SMTP测试失败: {str(e)}"
        )

@router.post("/configs/send-test-email", response_model=EmailSendResponse, summary="发送测试邮件")
def send_test_email(
    test_request: TestEmailRequest,
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """发送测试邮件"""
    try:
        email_service = EmailService(db)
        result = email_service.send_test_email(test_request.email)
        
        return EmailSendResponse(
            success=result["success"],
            message=result.get("error") if not result["success"] else "测试邮件发送成功",
            status_code=result.get("status_code")
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"发送测试邮件失败: {str(e)}"
        )

# 首页推荐管理
@router.get("/featured", summary="获取首页推荐动漫")
def get_featured_animes(
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    # 获取推荐列表并加载关联的动漫数据
    featured_list = db.query(FeaturedAnime).filter(
        FeaturedAnime.is_active == True
    ).order_by(FeaturedAnime.order_index).all()
    
    # 手动构建返回数据，确保包含完整的anime信息
    result = []
    for featured in featured_list:
        anime = db.query(Anime).filter(Anime.id == featured.anime_id).first()
        if anime:
            # 处理fanart字段
            fanart_data = anime.fanart
            if fanart_data:
                try:
                    import json
                    fanart_list = json.loads(fanart_data) if isinstance(fanart_data, str) else fanart_data
                except:
                    fanart_list = [fanart_data] if fanart_data else []
            else:
                fanart_list = []
            
            result.append({
                "id": featured.id,
                "anime_id": featured.anime_id,
                "order_index": featured.order_index,
                "custom_poster_url": featured.custom_poster_url,
                "is_active": featured.is_active,
                "anime": {
                    "id": anime.id,
                    "title": anime.title,
                    "title_english": anime.title_english,
                    "description": anime.description,
                    "cover": anime.cover,
                    "fanart": fanart_list,
                    "video_url": anime.video_url,
                    "view_count": anime.view_count,
                    "favorite_count": anime.favorite_count,
                    "release_year": anime.release_year,
                    "category": anime.category.name if anime.category else None,
                    "tags": [{"id": tag.id, "name": tag.name} for tag in anime.tags] if anime.tags else []
                }
            })
    
    return result

@router.post("/featured", summary="添加首页推荐动漫")
def add_featured_anime(
    featured_data: FeaturedAnimeCreate,
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    # 检查动漫是否存在
    anime = db.query(Anime).filter(Anime.id == featured_data.anime_id).first()
    if not anime:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="动漫不存在"
        )
    
    # 检查是否已经在推荐列表中
    existing = db.query(FeaturedAnime).filter(
        FeaturedAnime.anime_id == featured_data.anime_id,
        FeaturedAnime.is_active == True
    ).first()
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该动漫已在推荐列表中"
        )
    
    featured = FeaturedAnime(
        anime_id=featured_data.anime_id,
        order_index=featured_data.order_index,
        custom_poster_url=featured_data.custom_poster_url
    )
    db.add(featured)
    db.commit()
    db.refresh(featured)
    
    return {"message": "添加推荐成功", "id": featured.id}

@router.put("/featured/{featured_id}/update", summary="更新首页推荐动漫")
def update_featured_anime(
    featured_id: int,
    featured_update: FeaturedAnimeUpdate,
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    featured = db.query(FeaturedAnime).filter(FeaturedAnime.id == featured_id).first()
    if not featured:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="推荐项不存在"
        )
    
    # 更新字段
    if featured_update.order_index is not None:
        featured.order_index = featured_update.order_index
    if featured_update.custom_poster_url is not None:
        featured.custom_poster_url = featured_update.custom_poster_url
    if featured_update.is_active is not None:
        featured.is_active = featured_update.is_active
    
    db.commit()
    db.refresh(featured)
    
    return {"message": "推荐项更新成功", "id": featured.id}

@router.delete("/featured/{featured_id}", summary="移除首页推荐动漫")
def remove_featured_anime(
    featured_id: int,
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    featured = db.query(FeaturedAnime).filter(FeaturedAnime.id == featured_id).first()
    if not featured:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="推荐项不存在"
        )
    
    featured.is_active = False
    db.commit()
    
    return {"message": "移除推荐成功"}

# 用户管理
@router.get("/users", summary="获取用户列表")
def get_users(
    skip: int = 0,
    limit: int = 20,
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    users = db.query(UserModel).offset(skip).limit(limit).all()
    total = db.query(UserModel).count()
    
    return {
        "users": users,
        "total": total
    }

@router.get("/configs/player", summary="获取播放器配置")
def get_player_config(
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取播放器配置"""
    config_manager = ConfigManager(db)
    return config_manager.get_player_config()

@router.put("/users/{user_id}/status", summary="更新用户状态")
def update_user_status(
    user_id: int,
    is_active: bool,
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    user = db.query(UserModel).filter(UserModel.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    user.is_active = is_active
    db.commit()
    
    return {"message": f"用户状态更新为: {'启用' if is_active else '禁用'}"}