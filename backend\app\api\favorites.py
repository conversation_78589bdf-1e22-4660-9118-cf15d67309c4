from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.deps import get_current_active_user
from app.crud.anime import FavoriteCRUD
from app.schemas.anime import Favorite, FavoriteCreate
from app.models import User

router = APIRouter()

# 添加OPTIONS处理器来支持CORS预检
@router.options("/")
async def options_favorites():
    return {"message": "OK"}

@router.options("/check")
async def options_check():
    return {"message": "OK"}

@router.get("/", response_model=List[Favorite], summary="获取用户收藏列表")
def get_favorites(
    content_type: Optional[str] = Query(None, description="内容类型: anime, manga 或 all"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    if content_type and content_type not in ["anime", "manga", "all"]:
        raise HTTPException(status_code=400, detail="content_type 必须是 'anime', 'manga' 或 'all'")
    
    if content_type in ["anime", "manga"]:
        favorites = FavoriteCRUD.get_user_favorites_by_type(
            db, user_id=current_user.id, content_type=content_type, skip=skip, limit=limit
        )
    else:
        # 默认获取所有收藏或兼容旧版本
        if content_type == "all":
            favorites = FavoriteCRUD.get_all_user_favorites(
                db, user_id=current_user.id, skip=skip, limit=limit
            )
        else:
            # 兼容旧版本，默认返回动漫收藏
            favorites = FavoriteCRUD.get_user_favorites(
                db, user_id=current_user.id, skip=skip, limit=limit
            )
    
    return favorites

@router.post("/", summary="添加收藏")
def add_favorite(
    content_type: str = Query(..., description="内容类型: anime 或 manga"),
    content_id: int = Query(..., description="内容ID"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    if content_type not in ["anime", "manga"]:
        raise HTTPException(status_code=400, detail="content_type 必须是 'anime' 或 'manga'")
    
    # 检查是否已收藏
    existing = FavoriteCRUD.get_favorite_by_content(db, current_user.id, content_type, content_id)
    if existing:
        content_name = "动漫" if content_type == "anime" else "漫画"
        raise HTTPException(status_code=400, detail=f"已经收藏过该{content_name}")
    
    # 验证内容是否存在
    if content_type == "anime":
        from app.crud.anime import AnimeCRUD
        content = AnimeCRUD.get_anime_by_id(db, content_id)
        if not content:
            raise HTTPException(status_code=404, detail="动漫不存在")
    elif content_type == "manga":
        from app.crud.manga import MangaCRUD
        content = MangaCRUD.get_manga_basic(db, content_id)
        if not content:
            raise HTTPException(status_code=404, detail="漫画不存在")
    
    FavoriteCRUD.add_favorite_by_content(db, current_user.id, content_type, content_id)
    content_name = "动漫" if content_type == "anime" else "漫画"
    return {"message": f"{content_name}收藏成功"}

@router.delete("/", summary="取消收藏")
def remove_favorite(
    content_type: str = Query(..., description="内容类型: anime 或 manga"),
    content_id: int = Query(..., description="内容ID"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    if content_type not in ["anime", "manga"]:
        raise HTTPException(status_code=400, detail="content_type 必须是 'anime' 或 'manga'")
    
    success = FavoriteCRUD.remove_favorite_by_content(db, current_user.id, content_type, content_id)
    if not success:
        raise HTTPException(status_code=404, detail="收藏不存在")
    
    content_name = "动漫" if content_type == "anime" else "漫画"
    return {"message": f"取消{content_name}收藏成功"}

@router.get("/check", summary="检查是否已收藏")
def check_favorite(
    content_type: str = Query(..., description="内容类型: anime 或 manga"),
    content_id: int = Query(..., description="内容ID"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    if content_type not in ["anime", "manga"]:
        raise HTTPException(status_code=400, detail="content_type 必须是 'anime' 或 'manga'")
    
    favorite = FavoriteCRUD.get_favorite_by_content(db, current_user.id, content_type, content_id)
    return {"is_favorited": favorite is not None}

# 兼容旧版本的动漫收藏接口
@router.post("/anime", summary="添加动漫收藏（兼容旧版本）")
def add_anime_favorite(
    favorite: FavoriteCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    # 检查是否已收藏
    existing = FavoriteCRUD.get_favorite(db, current_user.id, favorite.anime_id)
    if existing:
        raise HTTPException(status_code=400, detail="已经收藏过该动漫")
    
    FavoriteCRUD.add_favorite(db, current_user.id, favorite.anime_id)
    return {"message": "收藏成功"}

@router.delete("/{anime_id}", summary="取消动漫收藏（兼容旧版本）")
def remove_anime_favorite(
    anime_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    success = FavoriteCRUD.remove_favorite(db, current_user.id, anime_id)
    if not success:
        raise HTTPException(status_code=404, detail="收藏不存在")
    
    return {"message": "取消收藏成功"}

@router.get("/{anime_id}/check", summary="检查动漫是否已收藏（兼容旧版本）")
def check_anime_favorite(
    anime_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    favorite = FavoriteCRUD.get_favorite(db, current_user.id, anime_id)
    return {"is_favorited": favorite is not None}