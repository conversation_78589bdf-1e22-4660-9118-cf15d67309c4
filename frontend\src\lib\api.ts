// API client for anime platform backend
import { getApiBaseUrl, logger } from './env';

const API_BASE_URL = getApiBaseUrl() + '/api/v1';

// Types
export interface User {
  id: number;
  username: string;
  email: string;
  is_active: boolean;
  is_admin: boolean;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface ConfigItem {
  value: string;
  description: string;
  type: string;
  category: string;
  required: boolean;
  sensitive?: boolean;
}

export interface SMTPConfig {
  host: string;
  port: number;
  use_tls: boolean;
  use_ssl: boolean;
  username: string;
  password: string;
  sender_name: string;
  sender_email: string;
}

export interface EmailSendResponse {
  success: boolean;
  message: string;
  status_code?: number;
}

export interface FeaturedAnime {
  id: number;
  anime_id: number;
  order_index: number;
  custom_poster_url?: string;
  is_active: boolean;
  anime: Anime;
}

// 漫画相关类型定义
export type MangaType = 'serial' | 'tankoubon' | 'doujinshi';
export type MangaStatus = 'ongoing' | 'completed' | 'hiatus' | 'cancelled';
export type ChapterStatus = 'draft' | 'published';

export interface MangaPage {
  id: number;
  chapter_id: number;
  page_number: number;
  image_url: string;
  width?: number;
  height?: number;
  file_size?: number;
  created_at: string;
}

export interface MangaChapter {
  id: number;
  manga_id: number;
  title?: string;
  chapter_number: number;
  volume_number?: number;
  page_count: number;
  is_free: boolean;
  price: number;
  status: ChapterStatus;
  release_date?: string;
  created_at: string;
  updated_at: string;
  pages?: MangaPage[];
}

export interface Manga {
  id: number;
  title: string;
  title_original?: string;
  category_id?: number;
  region_code?: string;
  manga_type?: MangaType;
  author?: string;
  artist?: string;
  publisher?: string;
  description?: string;
  cover?: string;
  banner?: string;
  view_count: number;
  favorite_count: number;
  chapter_count: number;
  status: MangaStatus;
  is_active: boolean;
  release_date?: string;
  created_at: string;
  updated_at: string;
  chapters?: MangaChapter[];
  tags?: Tag[];
  category?: Category;
}

export interface MangaReadingProgress {
  id: number;
  user_id: number;
  manga_id: number;
  chapter_id: number;
  page_number: number;
  total_pages: number;
  progress_percentage: number;
  last_read_at: string;
  created_at: string;
  updated_at: string;
  manga?: Manga;
  chapter?: MangaChapter;
}

// 书签相关类型定义
export interface MangaBookmark {
  id: number;
  user_id: number;
  manga_id: number;
  chapter_id: number;
  page_number: number;
  bookmark_name: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  manga?: Manga;
  chapter?: MangaChapter;
}

export interface BookmarkCreate {
  manga_id: number;
  chapter_id: number;
  page_number: number;
  bookmark_name: string;
  notes?: string;
}

export interface BookmarkUpdate {
  bookmark_name?: string;
  notes?: string;
}

export interface MangaListResponse {
  mangas: Manga[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface MangaChapterListResponse {
  chapters: MangaChapter[];
  total: number;
  manga_id: number;
}

export interface MangaPageListResponse {
  pages: MangaPage[];
  total: number;
  chapter_id: number;
}

export interface CreateMangaRequest {
  title: string;
  title_original?: string;
  category_id?: number;
  region_code?: string;
  manga_type?: MangaType;
  author?: string;
  artist?: string;
  publisher?: string;
  description?: string;
  cover?: string;
  banner?: string;
  status?: MangaStatus;
  is_active?: boolean;
  release_date?: string;
}

export interface UpdateMangaRequest {
  title?: string;
  title_original?: string;
  category_id?: number;
  region_code?: string;
  manga_type?: MangaType;
  author?: string;
  artist?: string;
  publisher?: string;
  description?: string;
  cover?: string;
  banner?: string;
  status?: MangaStatus;
  is_active?: boolean;
  release_date?: string;
}

export interface MangaSearchParams {
  skip?: number;
  limit?: number;
  category_id?: number;
  region_code?: string;
  manga_type?: MangaType;
  status?: MangaStatus;
  search?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface Category {
  id: number;
  name: string;
  num: number;
  created_at: string;
  updated_at: string;
}

export interface Anime {
  id: number;
  title: string;
  title_english?: string;
  title_japanese?: string;
  description?: string;
  cover?: string;
  fanart?: string | string[]; // 支持单个URL或URL数组
  video_url: string;
  release_year?: number;
  release_date?: string;
  view_count: number;
  favorite_count: number;
  is_active: boolean;
  category_id?: number;
  created_at: string;
  updated_at: string;
  // 标签数组
  tags?: Tag[];
  // 兼容旧前端显示，可选字段
  category?: string;
  // 自定义海报URL - 用于featured animes
  custom_poster_url?: string;
}

export interface Tag {
  id: number;
  name: string;
  name_english?: string;
  description?: string;
}

export interface Favorite {
  id: number;
  user_id: number;
  content_type: 'anime' | 'manga';
  anime_id?: number;
  manga_id?: number;
  created_at: string;
  anime?: Anime;
  manga?: Manga;
}

export interface Comment {
  id: number;
  user_id: number;
  anime_id?: number;
  manga_id?: number;
  content: string;
  attachments?: string[] | null;
  is_deleted: boolean;
  // 点赞统计字段
  like_count: number;
  dislike_count: number;
  // 编辑功能相关字段
  is_edited: boolean;
  edited_at?: string | null;
  edit_count: number;
  created_at: string;
  updated_at: string;
  // 新增回复和引用字段
  parent_id?: number | null;
  reply_to_user_id?: number | null;
  quoted_comment_id?: number | null;
  quoted_content?: string | null;
  // 关联对象（从后端返回）
  user?: User;
  parent_comment?: Comment;
  reply_to_user?: User;
  quoted_comment?: Comment;
  replies?: Comment[]; // 嵌套回复
  // 当前用户的点赞状态
  user_like_status?: boolean | null; // true=点赞, false=反对, null=未投票
}

export interface CommentCreate {
  anime_id?: number;
  manga_id?: number;
  content: string;
  attachments?: string[];
  // 新增回复和引用字段
  parent_id?: number;
  reply_to_user_id?: number;
  quoted_comment_id?: number;
  quoted_content?: string;
}

// 用户统计信息
export interface UserStats {
  user_info: {
    username: string;
    join_date: string;
    is_admin: boolean;
  };
  favorites: {
    total: number;
    anime_count: number;
    manga_count: number;
  };
  reading: {
    manga_reading_count: number;
  };
}

// 收藏统计
export interface FavoritesSummary {
  total: number;
  anime_count: number;
  manga_count: number;
}

// 用户资料更新
export interface UserProfileUpdate {
  username?: string;
  avatar_url?: string;
}

export interface AuthTokens {
  access_token: string;
  token_type: string;
  user: User;
}

export interface LoginData {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
}

export interface CreateAnimeRequest {
  title: string;
  title_english?: string;
  title_japanese?: string;
  description?: string;
  cover?: string;
  fanart?: string | string[];
  video_url?: string;
  release_year?: number;
  release_date?: string;
  category_id?: number;
  // 可选：前端展示使用（后端会忽略）
  tags?: string;
  category?: string;
}

export interface PlayerConfig {
  enable_ads: boolean;
  preroll_ad_url: string;
  midroll_ad_url: string;
  postroll_ad_url: string;
  enable_vast: boolean;
  vast_url: string;
  enable_rightclick: boolean;
  show_stats: boolean;
  show_version: boolean;
  skip_ad_time: number;
  ad_volume: number;
  autoplay: boolean;
  theme_color: string;
}

export interface UpdateAnimeRequest {
  title?: string;
  title_english?: string;
  title_japanese?: string;
  description?: string;
  cover?: string;
  fanart?: string | string[];
  video_url?: string;
  release_year?: number;
  release_date?: string;
  category_id?: number;
  // 可选：前端展示使用（后端会忽略）
  tags?: string;
  category?: string;
}

export interface AnimeCreateData {
  title: string;
  title_english?: string;
  description?: string;
  cover_url?: string;
  video_url: string;
  duration?: number;
  release_year?: number;
  tags?: string;
  category?: string;
}

class ApiClient {
  private baseURL: string;
  private cache = new Map<string, { data: any; timestamp: number }>();
  private cacheTimeout = 3 * 60 * 1000; // 3分钟缓存，适中的缓存时间

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private getCacheKey(url: string, params?: any): string {
    return `${url}_${JSON.stringify(params || {})}`;
  }

  private getCachedData<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  private setCachedData(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  private clearCache(): void {
    this.cache.clear();
  }

  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('access_token');
    return token
      ? { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      : { 'Content-Type': 'application/json' };
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      try {
        const errorData = await response.json();
        const errorMessage = errorData.message || errorData.detail || `HTTP ${response.status}: ${response.statusText}`;

        // 创建一个包含响应数据的错误对象
        const error = new Error(errorMessage) as any;
        error.response = {
          status: response.status,
          statusText: response.statusText,
          data: errorData
        };
        throw error;
      } catch (jsonError) {
        // 如果解析JSON失败，使用状态文本
        const error = new Error(`HTTP ${response.status}: ${response.statusText}`) as any;
        error.response = {
          status: response.status,
          statusText: response.statusText,
          data: null
        };
        throw error;
      }
    }
    return response.json();
  }

  // Auth endpoints
  async login(data: LoginData): Promise<AuthTokens> {
    const formData = new FormData();
    formData.append('username', data.username);
    formData.append('password', data.password);

    const response = await fetch(`${this.baseURL}/auth/login`, {
      method: 'POST',
      body: formData,
    });

    const tokens = await this.handleResponse<AuthTokens>(response);
    localStorage.setItem('access_token', tokens.access_token);
    return tokens;
  }

  async register(data: RegisterData): Promise<User> {
    const response = await fetch(`${this.baseURL}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    return this.handleResponse<User>(response);
  }

  async getCurrentUser(): Promise<User> {
    const response = await fetch(`${this.baseURL}/auth/me`, {
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<User>(response);
  }

  logout() {
    localStorage.removeItem('access_token');
  }

  // Admin endpoints
  async getAdminStats(): Promise<{
    total_users: number;
    total_animes: number;
    total_views: number;
    total_favorites: number;
  }> {
    const response = await fetch(`${this.baseURL}/auth/admin/stats`, {
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<{
      total_users: number;
      total_animes: number;
      total_views: number;
      total_favorites: number;
    }>(response);
  }

  // Configuration management endpoints
  async getGroupedConfigs(): Promise<{[key: string]: {configs: {[key: string]: ConfigItem}}}> {
    const response = await fetch(`${this.baseURL}/admin/configs/grouped`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<{[key: string]: {configs: {[key: string]: ConfigItem}}}>(response);
  }

  async getConfigsByCategory(category: string): Promise<{configs: {[key: string]: ConfigItem}}> {
    const response = await fetch(`${this.baseURL}/admin/configs/category/${category}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<{configs: {[key: string]: ConfigItem}}>(response);
  }

  async updateConfigsBatch(configs: {[key: string]: string}): Promise<{message: string; updated_count: number}> {
    const response = await fetch(`${this.baseURL}/admin/configs/batch`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ configs }),
    });
    return this.handleResponse<{message: string; updated_count: number}>(response);
  }

  async initDefaultConfigs(): Promise<{message: string; created_count: number}> {
    const response = await fetch(`${this.baseURL}/admin/configs/init-defaults`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<{message: string; created_count: number}>(response);
  }

  async testSMTPConfig(smtpConfig: SMTPConfig): Promise<{success: boolean; message?: string; error?: string}> {
    const response = await fetch(`${this.baseURL}/admin/configs/test-smtp`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(smtpConfig),
    });
    return this.handleResponse<{success: boolean; message?: string; error?: string}>(response);
  }

  async sendTestEmail(data: { to: string; subject: string; content: string }): Promise<EmailSendResponse> {
    const response = await fetch(`${this.baseURL}/admin/configs/send-test-email`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return this.handleResponse<EmailSendResponse>(response);
  }

  async getSmtpConfig(): Promise<SMTPConfig> {
    const response = await fetch(`${this.baseURL}/admin/configs/smtp`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<SMTPConfig>(response);
  }

  async updateSmtpConfig(config: SMTPConfig): Promise<{ message: string }> {
    const response = await fetch(`${this.baseURL}/admin/configs/smtp`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(config),
    });
    return this.handleResponse<{ message: string }>(response);
  }

  async testSmtpConnection(config: SMTPConfig): Promise<{ success: boolean; message?: string }> {
    const response = await fetch(`${this.baseURL}/admin/configs/test-smtp`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(config),
    });
    return this.handleResponse<{ success: boolean; message?: string }>(response);
  }

  async getFeaturedAnimes(limit: number = 6): Promise<{ animes: Anime[]; total: number }> {
    const response = await fetch(`${this.baseURL}/animes/featured?limit=${limit}`);
    return this.handleResponse<{ animes: Anime[]; total: number }>(response);
  }

  // Admin featured management
  async getAdminFeaturedAnimes(): Promise<FeaturedAnime[]> {
    const response = await fetch(`${this.baseURL}/admin/featured`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<FeaturedAnime[]>(response);
  }

  async addFeaturedAnime(animeId: number, orderIndex: number = 0, customPosterUrl?: string): Promise<{ message: string; id: number }> {
    const body: any = { anime_id: animeId, order_index: orderIndex };
    if (customPosterUrl) {
      body.custom_poster_url = customPosterUrl;
    }
    
    const response = await fetch(`${this.baseURL}/admin/featured`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(body),
    });
    return this.handleResponse<{ message: string; id: number }>(response);
  }

  async updateFeaturedAnime(featuredId: number, updates: {
    order_index?: number;
    custom_poster_url?: string;
    is_active?: boolean;
  }): Promise<{ message: string; id: number }> {
    const response = await fetch(`${this.baseURL}/admin/featured/${featuredId}/update`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(updates),
    });
    return this.handleResponse<{ message: string; id: number }>(response);
  }

  async removeFeaturedAnime(featuredId: number): Promise<{ message: string }> {
    const response = await fetch(`${this.baseURL}/admin/featured/${featuredId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<{ message: string }>(response);
  }

  // Player configuration endpoints
  async getPlayerConfig(): Promise<PlayerConfig> {
    const response = await fetch(`${this.baseURL}/animes/player-config`);
    return this.handleResponse<PlayerConfig>(response);
  }

  async getAdminPlayerConfig(): Promise<PlayerConfig> {
    const response = await fetch(`${this.baseURL}/admin/configs/player`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<PlayerConfig>(response);
  }

  // Anime endpoints
  async getAnimes(params?: {
    page?: number;
    limit?: number;
    skip?: number;
    category_id?: number;
    search?: string;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  }): Promise<{ animes: Anime[]; total: number }> {
    // 检查缓存（只缓存非搜索请求，搜索结果实时性要求高）
    if (!params?.search) {
      const cacheKey = this.getCacheKey('animes', params);
      const cached = this.getCachedData<{ animes: Anime[]; total: number }>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.skip) queryParams.append('skip', params.skip.toString());
    if (params?.category_id !== undefined) queryParams.append('category_id', String(params.category_id));
    if (params?.search) queryParams.append('search', params.search);
    if (params?.sort_by) queryParams.append('sort_by', params.sort_by);
    if (params?.sort_order) queryParams.append('sort_order', params.sort_order);

    const response = await fetch(`${this.baseURL}/animes/?${queryParams}`);
    const result = await this.handleResponse<{ animes: Anime[]; total: number } | Anime[]>(response);
    
    
    // 检查后端返回格式并相应处理
    let finalResult: { animes: Anime[]; total: number };
    if (result && typeof result === 'object' && 'animes' in result && 'total' in result) {
      // 新格式：{animes: [], total: number}
      finalResult = { animes: result.animes || [], total: result.total || 0 };
    } else if (Array.isArray(result)) {
      // 旧格式：直接返回数组
      finalResult = { animes: result, total: result.length };
    } else {
      // 异常格式处理
      finalResult = { animes: [], total: 0 };
    }

    // 缓存结果（只缓存非搜索请求）
    if (!params?.search) {
      const cacheKey = this.getCacheKey('animes', params);
      this.setCachedData(cacheKey, finalResult);
    }

    return finalResult;
  }

  async searchAnimes(title: string, params?: { skip?: number; limit?: number }): Promise<Anime[]> {
    const queryParams = new URLSearchParams();
    queryParams.append('title', title);
    if (params?.skip) queryParams.append('skip', params.skip.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const response = await fetch(`${this.baseURL}/animes/search?${queryParams}`);
    return this.handleResponse<Anime[]>(response);
  }

  async getAnime(id: number): Promise<Anime> {
    const response = await fetch(`${this.baseURL}/animes/${id}`);
    return this.handleResponse<Anime>(response);
  }

  async getAnimeRecommendations(id: number, limit: number = 12): Promise<{ recommendations: Anime[]; total: number }> {
    const response = await fetch(`${this.baseURL}/animes/${id}/recommendations?limit=${limit}`);
    return this.handleResponse<{ recommendations: Anime[]; total: number }>(response);
  }

  async createAnime(data: CreateAnimeRequest): Promise<Anime> {
    const response = await fetch(`${this.baseURL}/animes/`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });

    return this.handleResponse<Anime>(response);
  }

  async updateAnime(id: number, data: UpdateAnimeRequest): Promise<Anime> {
    const response = await fetch(`${this.baseURL}/animes/${id}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });

    return this.handleResponse<Anime>(response);
  }

  async deleteAnime(id: number): Promise<{ message: string }> {
    const response = await fetch(`${this.baseURL}/animes/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<{ message: string }>(response);
  }

  async incrementViewCount(id: number): Promise<{ message: string }> {
    const response = await fetch(`${this.baseURL}/animes/${id}/view`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<{ message: string }>(response);
  }

  // Favorites endpoints (updated to support both anime and manga)
  async getFavorites(params?: { 
    skip?: number; 
    limit?: number; 
    content_type?: 'anime' | 'manga' | 'all';
  }): Promise<Favorite[]> {
    const queryParams = new URLSearchParams();
    if (params?.skip) queryParams.append('skip', params.skip.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.content_type) queryParams.append('content_type', params.content_type);

    const response = await fetch(`${this.baseURL}/favorites/?${queryParams}`, {
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<Favorite[]>(response);
  }

  async addFavorite(contentType: 'anime' | 'manga', contentId: number): Promise<{ message: string }> {
    const queryParams = new URLSearchParams();
    queryParams.append('content_type', contentType);
    queryParams.append('content_id', contentId.toString());

    const response = await fetch(`${this.baseURL}/favorites/?${queryParams}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<{ message: string }>(response);
  }

  async removeFavorite(contentType: 'anime' | 'manga', contentId: number): Promise<{ message: string }> {
    const queryParams = new URLSearchParams();
    queryParams.append('content_type', contentType);
    queryParams.append('content_id', contentId.toString());

    const response = await fetch(`${this.baseURL}/favorites/?${queryParams}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<{ message: string }>(response);
  }

  async checkFavorite(contentType: 'anime' | 'manga', contentId: number): Promise<{ is_favorited: boolean }> {
    const queryParams = new URLSearchParams();
    queryParams.append('content_type', contentType);
    queryParams.append('content_id', contentId.toString());

    const response = await fetch(`${this.baseURL}/favorites/check?${queryParams}`, {
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<{ is_favorited: boolean }>(response);
  }

  // Legacy anime-only methods (for backward compatibility)
  async addAnimeFavorite(animeId: number): Promise<{ message: string }> {
    return this.addFavorite('anime', animeId);
  }

  async removeAnimeFavorite(animeId: number): Promise<{ message: string }> {
    return this.removeFavorite('anime', animeId);
  }

  async checkAnimeFavorite(animeId: number): Promise<{ is_favorited: boolean }> {
    return this.checkFavorite('anime', animeId);
  }

  // Comments endpoints
  async listComments(animeId: number, params?: { skip?: number; limit?: number }): Promise<Comment[]> {
    const queryParams = new URLSearchParams();
    if (params?.skip) queryParams.append('skip', String(params.skip));
    if (params?.limit) queryParams.append('limit', String(params.limit));
    const response = await fetch(`${this.baseURL}/comments/anime/${animeId}?${queryParams.toString()}`);
    return this.handleResponse<Comment[]>(response);
  }

  async createComment(animeId: number, data: CommentCreate): Promise<Comment> {
    const response = await fetch(`${this.baseURL}/comments/anime/${animeId}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return this.handleResponse<Comment>(response);
  }

  async listCommentsWithReplies(animeId: number, params?: { skip?: number; limit?: number }): Promise<Comment[]> {
    const queryParams = new URLSearchParams();
    if (params?.skip) queryParams.append('skip', String(params.skip));
    if (params?.limit) queryParams.append('limit', String(params.limit));
    const response = await fetch(`${this.baseURL}/comments/anime/${animeId}/threaded?${queryParams.toString()}`);
    return this.handleResponse<Comment[]>(response);
  }

  async getComment(commentId: number): Promise<Comment> {
    const response = await fetch(`${this.baseURL}/comments/${commentId}`);
    return this.handleResponse<Comment>(response);
  }

  async listCommentReplies(commentId: number, params?: { skip?: number; limit?: number }): Promise<Comment[]> {
    const queryParams = new URLSearchParams();
    if (params?.skip) queryParams.append('skip', String(params.skip));
    if (params?.limit) queryParams.append('limit', String(params.limit));
    const response = await fetch(`${this.baseURL}/comments/${commentId}/replies?${queryParams.toString()}`);
    return this.handleResponse<Comment[]>(response);
  }

  async deleteComment(commentId: number): Promise<{ message: string }> {
    const response = await fetch(`${this.baseURL}/comments/${commentId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<{ message: string }>(response);
  }

  // 编辑评论
  async updateComment(commentId: number, content: string): Promise<Comment> {
    const response = await fetch(`${this.baseURL}/comments/${commentId}?content=${encodeURIComponent(content)}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<Comment>(response);
  }

  // 点赞/反对评论
  async toggleCommentLike(commentId: number, isLike: boolean): Promise<{
    message: string;
    like_count: number;
    dislike_count: number;
    user_like_status: boolean | null;
  }> {
    const response = await fetch(`${this.baseURL}/comments/${commentId}/like?is_like=${isLike}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<{
      message: string;
      like_count: number;
      dislike_count: number;
      user_like_status: boolean | null;
    }>(response);
  }

  // 获取评论点赞状态
  async getCommentLikeStatus(commentId: number): Promise<{
    like_count: number;
    dislike_count: number;
    user_like_status: boolean | null;
  }> {
    const response = await fetch(`${this.baseURL}/comments/${commentId}/like-status`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<{
      like_count: number;
      dislike_count: number;
      user_like_status: boolean | null;
    }>(response);
  }

  // ==================== 漫画评论相关方法 ====================

  // 获取漫画评论列表（仅顶级评论）
  async listMangaComments(mangaId: number, params?: { skip?: number; limit?: number }): Promise<Comment[]> {
    const queryParams = new URLSearchParams();
    if (params?.skip) queryParams.append('skip', String(params.skip));
    if (params?.limit) queryParams.append('limit', String(params.limit));
    const response = await fetch(`${this.baseURL}/comments/manga/${mangaId}?${queryParams.toString()}`);
    return this.handleResponse<Comment[]>(response);
  }

  // 获取漫画评论列表（包含嵌套回复）
  async listMangaCommentsWithReplies(mangaId: number, params?: { skip?: number; limit?: number }): Promise<Comment[]> {
    const queryParams = new URLSearchParams();
    if (params?.skip) queryParams.append('skip', String(params.skip));
    if (params?.limit) queryParams.append('limit', String(params.limit));
    const response = await fetch(`${this.baseURL}/comments/manga/${mangaId}/threaded?${queryParams.toString()}`);
    return this.handleResponse<Comment[]>(response);
  }

  // 创建漫画评论
  async createMangaComment(data: CommentCreate): Promise<Comment> {
    const response = await fetch(`${this.baseURL}/comments/manga`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return this.handleResponse<Comment>(response);
  }

  // ==================== 漫画相关方法 ====================
  
  // 获取漫画列表
  async getMangas(params?: MangaSearchParams): Promise<MangaListResponse> {
    const queryParams = new URLSearchParams();
    if (params?.skip) queryParams.append('skip', params.skip.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.category_id) queryParams.append('category_id', params.category_id.toString());
    if (params?.region_code) queryParams.append('region_code', params.region_code);
    if (params?.manga_type) queryParams.append('manga_type', params.manga_type);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.search) queryParams.append('search', params.search);
    if (params?.sort_by) queryParams.append('sort_by', params.sort_by);
    if (params?.sort_order) queryParams.append('sort_order', params.sort_order);

    const response = await fetch(`${this.baseURL}/manga/?${queryParams}`);
    return this.handleResponse<MangaListResponse>(response);
  }

  // 获取热门漫画
  async getPopularMangas(limit: number = 10): Promise<Manga[]> {
    const response = await fetch(`${this.baseURL}/manga/popular?limit=${limit}`);
    return this.handleResponse<Manga[]>(response);
  }

  // 获取最近更新漫画
  async getRecentMangas(limit: number = 10): Promise<Manga[]> {
    const response = await fetch(`${this.baseURL}/manga/recent?limit=${limit}`);
    return this.handleResponse<Manga[]>(response);
  }

  // 根据标签搜索漫画
  async searchMangasByTags(tags: string, skip: number = 0, limit: number = 20): Promise<MangaListResponse> {
    const queryParams = new URLSearchParams();
    queryParams.append('tags', tags);
    queryParams.append('skip', skip.toString());
    queryParams.append('limit', limit.toString());

    const response = await fetch(`${this.baseURL}/manga/search/by-tags?${queryParams}`);
    return this.handleResponse<MangaListResponse>(response);
  }

  // 获取漫画详情
  async getManga(mangaId: number): Promise<Manga> {
    const response = await fetch(`${this.baseURL}/manga/${mangaId}`);
    return this.handleResponse<Manga>(response);
  }

  // 获取相关推荐漫画
  async getRelatedMangas(mangaId: number, limit: number = 10): Promise<Manga[]> {
    const response = await fetch(`${this.baseURL}/manga/${mangaId}/related?limit=${limit}`);
    return this.handleResponse<Manga[]>(response);
  }

  // 创建漫画（管理员）
  async createManga(data: CreateMangaRequest): Promise<Manga> {
    const response = await fetch(`${this.baseURL}/manga/`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return this.handleResponse<Manga>(response);
  }

  // 更新漫画（管理员）
  async updateManga(mangaId: number, data: UpdateMangaRequest): Promise<Manga> {
    const response = await fetch(`${this.baseURL}/manga/${mangaId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return this.handleResponse<Manga>(response);
  }

  // 删除漫画（管理员）
  async deleteManga(mangaId: number): Promise<{ message: string }> {
    const response = await fetch(`${this.baseURL}/manga/${mangaId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<{ message: string }>(response);
  }

  // 获取漫画章节列表
  async getMangaChapters(mangaId: number): Promise<MangaChapterListResponse> {
    const response = await fetch(`${this.baseURL}/manga/${mangaId}/chapters`);
    return this.handleResponse<MangaChapterListResponse>(response);
  }

  // 获取章节详情
  async getMangaChapter(chapterId: number): Promise<MangaChapter> {
    const response = await fetch(`${this.baseURL}/manga/chapters/${chapterId}`);
    return this.handleResponse<MangaChapter>(response);
  }

  // 通过章节号获取章节详情
  async getMangaChapterByNumber(mangaId: number, chapterNumber: number): Promise<MangaChapter> {
    const response = await fetch(`${this.baseURL}/manga/${mangaId}/chapters/${chapterNumber}`);
    return this.handleResponse<MangaChapter>(response);
  }

  // 获取章节页面列表
  async getChapterPages(chapterId: number): Promise<MangaPageListResponse> {
    const response = await fetch(`${this.baseURL}/manga/chapters/${chapterId}/pages`);
    return this.handleResponse<MangaPageListResponse>(response);
  }

  // 获取阅读进度
  async getMangaReadingProgress(mangaId: number): Promise<MangaReadingProgress> {
    const response = await fetch(`${this.baseURL}/manga/${mangaId}/reading-progress`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<MangaReadingProgress>(response);
  }

  // 更新阅读进度
  async updateMangaReadingProgress(
    mangaId: number,
    progress: {
      chapter_id: number;
      page_number: number;
      total_pages: number;
      progress_percentage: number;
    }
  ): Promise<MangaReadingProgress> {
    const response = await fetch(`${this.baseURL}/manga/${mangaId}/reading-progress`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({
        manga_id: mangaId,
        ...progress,
      }),
    });
    return this.handleResponse<MangaReadingProgress>(response);
  }

  // 删除阅读进度
  async deleteMangaReadingProgress(mangaId: number): Promise<{ message: string }> {
    const response = await fetch(`${this.baseURL}/manga/${mangaId}/reading-progress`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<{ message: string }>(response);
  }

  // 获取用户阅读历史
  async getMangaReadingHistory(skip: number = 0, limit: number = 20): Promise<MangaReadingProgress[]> {
    const queryParams = new URLSearchParams();
    queryParams.append('skip', skip.toString());
    queryParams.append('limit', limit.toString());

    const response = await fetch(`${this.baseURL}/manga/reading-history?${queryParams}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<MangaReadingProgress[]>(response);
  }

  // ==================== 书签相关方法 ====================
  
  // 获取用户书签列表
  async getUserBookmarks(skip: number = 0, limit: number = 50): Promise<MangaBookmark[]> {
    const queryParams = new URLSearchParams();
    queryParams.append('skip', skip.toString());
    queryParams.append('limit', limit.toString());

    const response = await fetch(`${this.baseURL}/manga/bookmarks?${queryParams}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<MangaBookmark[]>(response);
  }

  // 添加书签
  async addBookmark(bookmarkData: BookmarkCreate): Promise<MangaBookmark> {
    const response = await fetch(`${this.baseURL}/manga/bookmarks`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(bookmarkData),
    });
    return this.handleResponse<MangaBookmark>(response);
  }

  // 更新书签
  async updateBookmark(bookmarkId: number, updateData: BookmarkUpdate): Promise<MangaBookmark> {
    const response = await fetch(`${this.baseURL}/manga/bookmarks/${bookmarkId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(updateData),
    });
    return this.handleResponse<MangaBookmark>(response);
  }

  // 删除书签
  async removeBookmark(bookmarkData: { manga_id: number; chapter_id: number; page_number: number }): Promise<{ message: string }> {
    const queryParams = new URLSearchParams();
    queryParams.append('manga_id', bookmarkData.manga_id.toString());
    queryParams.append('chapter_id', bookmarkData.chapter_id.toString());
    queryParams.append('page_number', bookmarkData.page_number.toString());

    const response = await fetch(`${this.baseURL}/manga/bookmarks?${queryParams}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<{ message: string }>(response);
  }

  // 检查特定页面是否已添加书签
  async checkBookmark(mangaId: number, chapterId: number, pageNumber: number): Promise<MangaBookmark | null> {
    const queryParams = new URLSearchParams();
    queryParams.append('manga_id', mangaId.toString());
    queryParams.append('chapter_id', chapterId.toString());
    queryParams.append('page_number', pageNumber.toString());

    try {
      const response = await fetch(`${this.baseURL}/manga/bookmarks/check?${queryParams}`, {
        headers: this.getAuthHeaders(),
      });
      return this.handleResponse<MangaBookmark | null>(response);
    } catch {
      return null;
    }
  }

  // User profile and statistics endpoints
  async getUserProfile(): Promise<User> {
    const response = await fetch(`${this.baseURL}/user/profile`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<User>(response);
  }

  async updateUserProfile(data: UserProfileUpdate): Promise<{ message: string; user: User }> {
    const queryParams = new URLSearchParams();
    if (data.username) queryParams.append('username', data.username);
    if (data.avatar_url !== undefined) queryParams.append('avatar_url', data.avatar_url);

    const response = await fetch(`${this.baseURL}/user/profile?${queryParams}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<{ message: string; user: User }>(response);
  }

  async getUserStats(): Promise<UserStats> {
    const response = await fetch(`${this.baseURL}/user/stats`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<UserStats>(response);
  }

  async getFavoritesSummary(): Promise<FavoritesSummary> {
    const response = await fetch(`${this.baseURL}/user/favorites/summary`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<FavoritesSummary>(response);
  }

  async getUserFavorites(params?: {
    content_type?: 'anime' | 'manga' | 'all';
    skip?: number;
    limit?: number;
  }): Promise<Favorite[]> {
    const queryParams = new URLSearchParams();
    if (params?.content_type) queryParams.append('content_type', params.content_type);
    if (params?.skip) queryParams.append('skip', params.skip.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const response = await fetch(`${this.baseURL}/user/favorites?${queryParams}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<Favorite[]>(response);
  }

  async getUserReadingHistory(params?: { skip?: number; limit?: number }): Promise<MangaReadingProgress[]> {
    const queryParams = new URLSearchParams();
    if (params?.skip) queryParams.append('skip', params.skip.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const response = await fetch(`${this.baseURL}/user/reading-history?${queryParams}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<MangaReadingProgress[]>(response);
  }

  async deleteUserReadingHistory(mangaId: number): Promise<{ message: string }> {
    const response = await fetch(`${this.baseURL}/user/reading-history/${mangaId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<{ message: string }>(response);
  }

  async clearUserReadingHistory(): Promise<{ message: string }> {
    const response = await fetch(`${this.baseURL}/user/reading-history`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<{ message: string }>(response);
  }

  // Password change endpoint
  async changePassword(data: {
    old_password: string;
    new_password: string;
    confirm_password: string;
  }): Promise<{ message: string }> {
    const response = await fetch(`${this.baseURL}/user/password`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return this.handleResponse<{ message: string }>(response);
  }

  // Account deletion endpoint
  async deleteAccount(username_confirmation: string): Promise<{ message: string }> {
    const response = await fetch(`${this.baseURL}/user/account`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ username_confirmation }),
    });
    return this.handleResponse<{ message: string }>(response);
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!localStorage.getItem('access_token');
  }

  // Admin User Management APIs
  async getUsers(skip: number = 0, limit: number = 20, search?: string): Promise<any> {
    const queryParams = new URLSearchParams();
    queryParams.append('skip', skip.toString());
    queryParams.append('limit', limit.toString());
    if (search && search.trim()) {
      queryParams.append('search', search.trim());
    }

    const response = await fetch(`${this.baseURL}/admin/users?${queryParams}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<any>(response);
  }

  async updateUser(userId: number, data: { is_active?: boolean; is_admin?: boolean; is_banned?: boolean; role?: string }): Promise<{ message: string }> {
    const response = await fetch(`${this.baseURL}/admin/users/${userId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return this.handleResponse<{ message: string }>(response);
  }

  async deleteUser(userId: number): Promise<{ message: string }> {
    const response = await fetch(`${this.baseURL}/admin/users/${userId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<{ message: string }>(response);
  }

  // Admin Email APIs
  async sendBulkEmail(data: {
    subject: string;
    content: string;
    recipients: string[];
  }): Promise<{ message: string; sent_count: number }> {
    const response = await fetch(`${this.baseURL}/admin/email/bulk`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return this.handleResponse<{ message: string; sent_count: number }>(response);
  }

  // Admin Advertisement APIs
  async updateAdPlacements(placements: any[]): Promise<{ message: string }> {
    const response = await fetch(`${this.baseURL}/admin/ads/placements`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ placements }),
    });
    return this.handleResponse<{ message: string }>(response);
  }

  async getAdPlacements(): Promise<any[]> {
    const response = await fetch(`${this.baseURL}/admin/ads/placements`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<any[]>(response);
  }

  // Announcements management
  async getAnnouncements(params?: { filter?: string }): Promise<any[]> {
    const queryParams = new URLSearchParams();
    if (params?.filter) queryParams.append('filter', params.filter);

    const response = await fetch(`${this.baseURL}/admin/announcements?${queryParams}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<any[]>(response);
  }

  async createAnnouncement(data: any): Promise<any> {
    const response = await fetch(`${this.baseURL}/admin/announcements`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return this.handleResponse<any>(response);
  }

  async updateAnnouncement(id: number, data: any): Promise<any> {
    const response = await fetch(`${this.baseURL}/admin/announcements/${id}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return this.handleResponse<any>(response);
  }

  async deleteAnnouncement(id: number): Promise<{ message: string }> {
    const response = await fetch(`${this.baseURL}/admin/announcements/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<{ message: string }>(response);
  }
}

export const apiClient = new ApiClient();