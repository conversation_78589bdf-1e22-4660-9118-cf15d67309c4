'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Anime, CreateAnimeRequest, apiClient, Tag, FeaturedAnime, ConfigItem, SMTPConfig } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';
import { Plus, Edit, Trash2, Save, X, Search, Users, BarChart3 } from 'lucide-react';

interface AnimeFormData extends Omit<CreateAnimeRequest, 'tags'> {
  tags: string;
}

export default function AdminPage() {
  const [animes, setAnimes] = useState<Anime[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingAnime, setEditingAnime] = useState<Anime | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalAnimes, setTotalAnimes] = useState(0);
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalAnimes: 0,
    totalViews: 0,
    totalFavorites: 0
  });

  // Featured management states
  const [featuredAnimes, setFeaturedAnimes] = useState<FeaturedAnime[]>([]);
  const [featuredLoading, setFeaturedLoading] = useState(false);
  const [featuredSearchTerm, setFeaturedSearchTerm] = useState('');
  const [featuredSearchResults, setFeaturedSearchResults] = useState<Anime[]>([]);
  const [editingFeatured, setEditingFeatured] = useState<FeaturedAnime | null>(null);
  const [customPosterUrl, setCustomPosterUrl] = useState('');

  // Configuration management states
  const [configs, setConfigs] = useState<{[key: string]: {configs: {[key: string]: ConfigItem}}}>();
  const [configsLoading, setConfigsLoading] = useState(false);
  const [activeConfigTab, setActiveConfigTab] = useState('general');
  const [configChanges, setConfigChanges] = useState<{[key: string]: string}>({});
  const [smtpTesting, setSmtpTesting] = useState(false);
  const [emailTesting, setEmailTesting] = useState(false);

  const { isAdmin, isAuthenticated } = useAuth();
  const router = useRouter();

  const [formData, setFormData] = useState<AnimeFormData>({
    title: '',
    title_english: '',
    description: '',
    category: '',
    tags: '',
    cover: '',
    fanart: '',
    video_url: '',
    release_year: new Date().getFullYear(),
  });

  const categories = ['动作', '冒险', '喜剧', '剧情', '奇幻', '恐怖', '浪漫', '科幻', '惊悚'];

  // 辅助函数：将Tag[]转换为逗号分隔的字符串
  const formatTagsForEdit = (tags?: Tag[] | string): string => {
    if (!tags) return '';
    if (typeof tags === 'string') return tags;
    if (Array.isArray(tags)) {
      return tags.map(tag => tag.name).join(', ');
    }
    return '';
  };

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth');
      return;
    }
    if (!isAdmin) {
      router.push('/');
      return;
    }
    
    fetchAnimes();
    fetchStats();
    fetchFeaturedAnimes();
    fetchConfigs();
  }, [isAuthenticated, isAdmin, currentPage]);

  const fetchAnimes = async () => {
    try {
      setLoading(true);
      const response = await apiClient.getAnimes({
        page: currentPage,
        limit: 10,
        search: searchTerm || undefined
      });
      setAnimes(response.animes);
      setTotalPages(Math.ceil(response.total / 10));
      setTotalAnimes(response.total);
    } catch (error) {
      console.error('Failed to fetch animes:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // 使用新的管理员统计API
      const statsData = await apiClient.getAdminStats();
      
      setStats({
        totalUsers: statsData.total_users,
        totalAnimes: statsData.total_animes,
        totalViews: statsData.total_views,
        totalFavorites: statsData.total_favorites
      });
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  // Featured management functions
  const fetchFeaturedAnimes = async () => {
    try {
      setFeaturedLoading(true);
      const featured = await apiClient.getAdminFeaturedAnimes();
      console.log('Fetched featured animes:', featured); // Debug log
      setFeaturedAnimes(featured);
    } catch (error) {
      console.error('Failed to fetch featured animes:', error);
      setFeaturedAnimes([]);
    } finally {
      setFeaturedLoading(false);
    }
  };

  const searchAnimesForFeatured = async () => {
    if (!featuredSearchTerm.trim()) {
      setFeaturedSearchResults([]);
      return;
    }

    try {
      const response = await apiClient.getAnimes({
        search: featuredSearchTerm,
        limit: 10
      });
      setFeaturedSearchResults(response.animes);
    } catch (error) {
      console.error('Failed to search animes:', error);
      setFeaturedSearchResults([]);
    }
  };

  const addToFeatured = async (animeId: number, customPosterUrl?: string) => {
    try {
      const nextOrderIndex = featuredAnimes.length;
      await apiClient.addFeaturedAnime(animeId, nextOrderIndex, customPosterUrl);
      await fetchFeaturedAnimes();
      setFeaturedSearchTerm('');
      setFeaturedSearchResults([]);
      alert('添加到推荐成功！');
    } catch (error: unknown) {
      console.error('Failed to add featured anime:', error);
      const errorMessage = error instanceof Error ? error.message : '添加失败';
      alert(errorMessage);
    }
  };

  const startEditFeatured = (featured: FeaturedAnime) => {
    setEditingFeatured(featured);
    setCustomPosterUrl(featured.custom_poster_url || '');
  };

  const saveEditFeatured = async () => {
    if (!editingFeatured) return;

    try {
      await apiClient.updateFeaturedAnime(editingFeatured.id, {
        custom_poster_url: customPosterUrl.trim() || undefined
      });
      await fetchFeaturedAnimes();
      setEditingFeatured(null);
      setCustomPosterUrl('');
      alert('更新成功！');
    } catch (error: unknown) {
      console.error('Failed to update featured anime:', error);
      const errorMessage = error instanceof Error ? error.message : '更新失败';
      alert(errorMessage);
    }
  };

  const cancelEditFeatured = () => {
    setEditingFeatured(null);
    setCustomPosterUrl('');
  };

  const removeFeatured = async (featuredId: number) => {
    if (!confirm('确定要从推荐中移除这个动漫吗？')) return;

    try {
      console.log('Removing featured anime with ID:', featuredId); // Debug log
      const result = await apiClient.removeFeaturedAnime(featuredId);
      console.log('Remove result:', result); // Debug log
      await fetchFeaturedAnimes();
      alert('移除成功！');
    } catch (error) {
      console.error('Failed to remove featured anime:', error);
      alert('移除失败');
    }
  };

  // Configuration management functions
  const fetchConfigs = async () => {
    try {
      setConfigsLoading(true);
      const groupedConfigs = await apiClient.getGroupedConfigs();
      console.log('Fetched configs:', groupedConfigs);
      setConfigs(groupedConfigs);
    } catch (error) {
      console.error('Failed to fetch configs:', error);
    } finally {
      setConfigsLoading(false);
    }
  };

  const handleConfigChange = (key: string, value: string) => {
    setConfigChanges(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveConfigs = async () => {
    if (Object.keys(configChanges).length === 0) {
      alert('没有配置更改');
      return;
    }

    try {
      const result = await apiClient.updateConfigsBatch(configChanges);
      setConfigChanges({});
      await fetchConfigs();
      alert(`保存成功：${result.message}`);
    } catch (error: unknown) {
      console.error('Failed to save configs:', error);
      const errorMessage = error instanceof Error ? error.message : '保存失败';
      alert(errorMessage);
    }
  };

  const testSMTPConnection = async () => {
    if (!configs?.email?.configs) return;

    const emailConfigs = configs.email.configs;
    const smtpConfig: SMTPConfig = {
      host: configChanges.smtp_host || emailConfigs.smtp_host?.value || '',
      port: parseInt(configChanges.smtp_port || emailConfigs.smtp_port?.value || '587'),
      use_tls: (configChanges.smtp_use_tls || emailConfigs.smtp_use_tls?.value || 'true') === 'true',
      use_ssl: (configChanges.smtp_use_ssl || emailConfigs.smtp_use_ssl?.value || 'false') === 'true',
      username: configChanges.smtp_username || emailConfigs.smtp_username?.value || '',
      password: configChanges.smtp_password || emailConfigs.smtp_password?.value || ''
    };

    if (!smtpConfig.host || !smtpConfig.username) {
      alert('请先配置SMTP服务器信息');
      return;
    }

    try {
      setSmtpTesting(true);
      const result = await apiClient.testSMTPConfig(smtpConfig);
      alert(result.success ? (result.message || 'SMTP配置测试成功') : (result.error || 'SMTP配置测试失败'));
    } catch (error: unknown) {
      console.error('SMTP test failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'SMTP测试失败';
      alert(errorMessage);
    } finally {
      setSmtpTesting(false);
    }
  };

  const sendTestEmail = async () => {
    const testEmail = prompt('请输入测试邮箱地址：');
    if (!testEmail) return;

    try {
      setEmailTesting(true);
      const result = await apiClient.sendTestEmail(testEmail);
      alert(result.success ? '测试邮件发送成功！请检查邮箱。' : `发送失败：${result.message}`);
    } catch (error: unknown) {
      console.error('Test email failed:', error);
      const errorMessage = error instanceof Error ? error.message : '发送测试邮件失败';
      alert(errorMessage);
    } finally {
      setEmailTesting(false);
    }
  };

  const initDefaultConfigs = async () => {
    if (!confirm('确定要初始化默认配置吗？这将创建所有缺失的默认配置项。')) return;

    try {
      const result = await apiClient.initDefaultConfigs();
      await fetchConfigs();
      alert(`初始化成功：${result.message}`);
    } catch (error: unknown) {
      console.error('Init default configs failed:', error);
      const errorMessage = error instanceof Error ? error.message : '初始化默认配置失败';
      alert(errorMessage);
    }
  };

  const handleSearch = () => {
    setCurrentPage(1);
    fetchAnimes();
  };

  const handleCreateAnime = () => {
    setIsCreating(true);
    setEditingAnime(null);
    setFormData({
      title: '',
      title_english: '',
      description: '',
      category: '',
      tags: '',
      cover: '',
      fanart: '',
      video_url: '',
      release_year: new Date().getFullYear(),
    });
  };

  const handleEditAnime = (anime: Anime) => {
    setEditingAnime(anime);
    setIsCreating(false);
    setFormData({
      title: anime.title,
      title_english: anime.title_english || '',
      description: anime.description || '',
      category: anime.category || '',
      tags: formatTagsForEdit(anime.tags),
      cover: anime.cover || '',
      fanart: anime.fanart || '',
      video_url: anime.video_url || '',
      release_year: anime.release_year || new Date().getFullYear(),
    });
  };

  const handleSaveAnime = async () => {
    try {
      const animeData = {
        ...formData,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean).join(',')
      };

      if (isCreating) {
        await apiClient.createAnime(animeData);
      } else if (editingAnime) {
        await apiClient.updateAnime(editingAnime.id, animeData);
      }

      setIsCreating(false);
      setEditingAnime(null);
      fetchAnimes();
      fetchStats();
    } catch (error: unknown) {
      console.error('Failed to save anime:', error);
      alert('保存失败，请检查输入信息');
    }
  };

  const handleDeleteAnime = async (id: number) => {
    if (confirm('确定要删除这部动漫吗？此操作不可撤销。')) {
      try {
        await apiClient.deleteAnime(id);
        fetchAnimes();
        fetchStats();
      } catch (error) {
        console.error('Failed to delete anime:', error);
        alert('删除失败');
      }
    }
  };

  const handleCancelEdit = () => {
    setIsCreating(false);
    setEditingAnime(null);
  };

  const handleFormChange = (field: keyof AnimeFormData, value: string | number | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (!isAuthenticated || !isAdmin) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <p className="text-lg text-muted-foreground">
            正在验证管理员权限...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">管理面板</h1>
          <p className="text-muted-foreground">管理动漫内容和用户数据</p>
        </div>
        <Button onClick={handleCreateAnime}>
          <Plus className="w-4 h-4 mr-2" />
          添加动漫
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="animes">动漫管理</TabsTrigger>
          <TabsTrigger value="featured">推荐配置</TabsTrigger>
          <TabsTrigger value="settings">系统设置</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总用户数</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalUsers}</div>
                <p className="text-xs text-muted-foreground">注册用户</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总动漫数</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalAnimes}</div>
                <p className="text-xs text-muted-foreground">已发布的动漫</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总观看数</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalViews}</div>
                <p className="text-xs text-muted-foreground">累计观看次数</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总收藏数</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalFavorites}</div>
                <p className="text-xs text-muted-foreground">累计收藏次数</p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>最近添加的动漫</CardTitle>
              <CardDescription>最新发布的动漫内容</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
                {animes.slice(0, 5).map((anime) => (
                  <div key={anime.id} className="space-y-2">
                    <div className="aspect-[3/4] overflow-hidden rounded-lg">
                      <img
        src={anime.cover || '/placeholder-anime.jpg'}
                        alt={anime.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/placeholder-anime.jpg';
                        }}
                      />
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium line-clamp-2">{anime.title}</p>
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>{anime.view_count} 观看</span>
                        <span>{anime.favorite_count} 收藏</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="animes" className="space-y-6">
          {/* Search */}
          <div className="flex gap-2">
            <Input
              placeholder="搜索动漫..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="flex-1"
            />
            <Button onClick={handleSearch}>
              <Search className="w-4 h-4" />
            </Button>
          </div>

          {/* Create/Edit Form */}
          {(isCreating || editingAnime) && (
            <Card>
              <CardHeader>
                <CardTitle>
                  {isCreating ? '添加动漫' : '编辑动漫'}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">标题 *</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => handleFormChange('title', e.target.value)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="title_english">英文标题</Label>
                    <Input
                      id="title_english"
                      value={formData.title_english}
                      onChange={(e) => handleFormChange('title_english', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="category">分类</Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) => handleFormChange('category', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择分类" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="release_year">发行年份</Label>
                    <Input
                      id="release_year"
                      type="number"
                      min="1900"
                      max={new Date().getFullYear() + 10}
                      value={formData.release_year}
                      onChange={(e) => handleFormChange('release_year', parseInt(e.target.value) || new Date().getFullYear())}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="cover">封面图片URL（cover）</Label>
                    <Input
                      id="cover"
                      value={formData.cover}
                      onChange={(e) => handleFormChange('cover', e.target.value)}
                      placeholder="https://example.com/cover.jpg"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="fanart">预览图URL（fanart）</Label>
                    <Input
                      id="fanart"
                      value={formData.fanart}
                      onChange={(e) => handleFormChange('fanart', e.target.value)}
                      placeholder="https://example.com/fanart.jpg"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="video_url">视频URL</Label>
                    <Input
                      id="video_url"
                      value={formData.video_url}
                      onChange={(e) => handleFormChange('video_url', e.target.value)}
                      placeholder="https://example.com/video.mp4"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tags">标签 (用逗号分隔)</Label>
                  <Input
                    id="tags"
                    value={formData.tags}
                    onChange={(e) => handleFormChange('tags', e.target.value)}
                    placeholder="标签1, 标签2, 标签3"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">描述</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleFormChange('description', e.target.value)}
                    rows={4}
                  />
                </div>

                {/* 移除是否推荐开关（is_featured 已删除） */}

                <div className="flex gap-2">
                  <Button onClick={handleSaveAnime}>
                    <Save className="w-4 h-4 mr-2" />
                    保存
                  </Button>
                  <Button variant="outline" onClick={handleCancelEdit}>
                    <X className="w-4 h-4 mr-2" />
                    取消
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Anime List */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>动漫列表 ({totalAnimes})</CardTitle>
                <span className="text-sm text-muted-foreground">
                  第 {currentPage} 页，共 {totalPages} 页
                </span>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">加载中...</div>
              ) : animes.length > 0 ? (
                <>
                  <div className="space-y-4">
                    {animes.map((anime) => (
                      <div key={anime.id} className="flex items-center gap-4 p-4 border rounded-lg">
                        <img
                        src={anime.cover || '/placeholder-anime.jpg'}
                          alt={anime.title}
                          className="w-16 h-20 object-cover rounded"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = '/placeholder-anime.jpg';
                          }}
                        />
                        
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold">{anime.title}</h3>
                            {/* 移除推荐标识 */}
                          </div>
                          
                          {anime.title_english && (
                            <p className="text-sm text-muted-foreground">{anime.title_english}</p>
                          )}
                          
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            {anime.category && (
                              <Badge variant="outline">{anime.category}</Badge>
                            )}
                            <span>{anime.release_year}</span>
                            <span>{anime.view_count} 观看</span>
                            <span>{anime.favorite_count} 收藏</span>
                          </div>

                          {anime.description && (
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {anime.description}
                            </p>
                          )}
                        </div>

                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEditAnime(anime)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleDeleteAnime(anime.id)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="flex justify-center gap-2 mt-6">
                      <Button
                        variant="outline"
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                      >
                        上一页
                      </Button>
                      <span className="flex items-center px-4">
                        {currentPage} / {totalPages}
                      </span>
                      <Button
                        variant="outline"
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                      >
                        下一页
                      </Button>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">暂无动漫数据</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="featured" className="space-y-6">
          {/* Add Featured Anime Section */}
          <Card>
            <CardHeader>
              <CardTitle>添加推荐动漫</CardTitle>
              <CardDescription>搜索并添加动漫到首页轮播</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="搜索动漫标题..."
                  value={featuredSearchTerm}
                  onChange={(e) => setFeaturedSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && searchAnimesForFeatured()}
                  className="flex-1"
                />
                <Button onClick={searchAnimesForFeatured}>
                  <Search className="w-4 h-4" />
                </Button>
              </div>

              {/* Search Results */}
              {featuredSearchResults.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium">搜索结果：</h4>
                  <div className="max-h-64 overflow-y-auto space-y-2">
                    {featuredSearchResults.map((anime) => (
                      <div key={anime.id} className="flex items-center gap-3 p-3 border rounded-lg">
                        <img
                          src={anime.cover || ''}
                          alt={anime.title}
                          className="w-12 h-16 object-cover rounded"
                        />
                        <div className="flex-1">
                          <h5 className="font-medium">{anime.title}</h5>
                          {anime.title_english && (
                            <p className="text-sm text-muted-foreground">{anime.title_english}</p>
                          )}
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span>{anime.view_count} 观看</span>
                            <span>{anime.favorite_count} 收藏</span>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          onClick={() => addToFeatured(anime.id)}
                          disabled={featuredAnimes.some(fa => fa.anime_id === anime.id)}
                        >
                          {featuredAnimes.some(fa => fa.anime_id === anime.id) ? '已推荐' : '添加'}
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Current Featured Animes */}
          <Card>
            <CardHeader>
              <CardTitle>当前推荐列表</CardTitle>
              <CardDescription>
                首页轮播显示的动漫 (最多6个，按顺序显示)
              </CardDescription>
            </CardHeader>
            <CardContent>
              {featuredLoading ? (
                <div className="text-center py-8">加载中...</div>
              ) : featuredAnimes.length > 0 ? (
                <div className="space-y-4">
                  {featuredAnimes.map((featured, index) => (
                    <div key={featured.id} className="border rounded-lg">
                      {/* 编辑模式 */}
                      {editingFeatured?.id === featured.id ? (
                        <div className="p-4 space-y-4">
                          <div className="flex items-center gap-4">
                            <span className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                              {index + 1}
                            </span>
                            <img
                              src={customPosterUrl || featured.anime.cover || 'https://picsum.photos/80/100?blur=3'}
                              alt={featured.anime.title}
                              className="w-16 h-20 object-cover rounded"
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = 'https://picsum.photos/80/100?blur=3';
                              }}
                            />
                            <div className="flex-1">
                              <h3 className="font-semibold">{featured.anime.title}</h3>
                              {featured.anime.title_english && (
                                <p className="text-sm text-muted-foreground">{featured.anime.title_english}</p>
                              )}
                            </div>
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="custom-poster">自定义海报URL (可选)</Label>
                            <Input
                              id="custom-poster"
                              type="url"
                              value={customPosterUrl}
                              onChange={(e) => setCustomPosterUrl(e.target.value)}
                              placeholder="输入自定义海报图片URL，留空使用默认封面"
                            />
                            <p className="text-xs text-muted-foreground">
                              自定义海报将在首页轮播中显示，推荐尺寸: 1920x1080
                            </p>
                          </div>
                          
                          <div className="flex gap-2">
                            <Button size="sm" onClick={saveEditFeatured}>
                              <Save className="w-4 h-4 mr-2" />
                              保存
                            </Button>
                            <Button size="sm" variant="outline" onClick={cancelEditFeatured}>
                              <X className="w-4 h-4 mr-2" />
                              取消
                            </Button>
                          </div>
                        </div>
                      ) : (
                        /* 正常显示模式 */
                        <div className="flex items-center gap-4 p-4">
                          <div className="flex items-center gap-2">
                            <span className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                              {index + 1}
                            </span>
                          </div>
                          
                          <div className="relative">
                            <img
                              src={featured.custom_poster_url || featured.anime.cover || 'https://picsum.photos/80/100?blur=3'}
                              alt={featured.anime.title}
                              className="w-16 h-20 object-cover rounded"
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = 'https://picsum.photos/80/100?blur=3';
                              }}
                            />
                            {featured.custom_poster_url && (
                              <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center">
                                ✓
                              </div>
                            )}
                          </div>
                          
                          <div className="flex-1">
                            <h3 className="font-semibold">{featured.anime.title}</h3>
                            {featured.anime.title_english && (
                              <p className="text-sm text-muted-foreground">{featured.anime.title_english}</p>
                            )}
                            <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                              <span>{featured.anime.view_count} 观看</span>
                              <span>{featured.anime.favorite_count} 收藏</span>
                              {featured.anime.release_year && <span>{featured.anime.release_year}</span>}
                            </div>
                            {featured.custom_poster_url && (
                              <div className="flex items-center gap-1 text-xs text-blue-600 mt-1">
                                <span>🎨</span>
                                <span>使用自定义海报</span>
                              </div>
                            )}
                          </div>

                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => startEditFeatured(featured)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => removeFeatured(featured.id)}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                  
                  {featuredAnimes.length >= 6 && (
                    <div className="text-center text-sm text-muted-foreground">
                      最多只能添加6个推荐动漫
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">暂无推荐动漫</p>
                  <p className="text-sm text-muted-foreground mt-2">
                    使用上方搜索功能添加推荐动漫
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>系统设置</CardTitle>
                  <CardDescription>配置网站基本信息和邮件服务</CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    onClick={initDefaultConfigs}
                    disabled={configsLoading}
                  >
                    初始化默认配置
                  </Button>
                  <Button 
                    onClick={saveConfigs}
                    disabled={configsLoading || Object.keys(configChanges).length === 0}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    保存更改 {Object.keys(configChanges).length > 0 && `(${Object.keys(configChanges).length})`}
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {configsLoading ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">加载配置中...</p>
                </div>
              ) : configs ? (
                <Tabs value={activeConfigTab} onValueChange={setActiveConfigTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="general">网站信息</TabsTrigger>
                    <TabsTrigger value="email">邮件配置</TabsTrigger>
                    <TabsTrigger value="users">用户管理</TabsTrigger>
                    <TabsTrigger value="player">播放器配置</TabsTrigger>
                  </TabsList>

                  {/* 网站基本信息配置 */}
                  <TabsContent value="general" className="space-y-4 mt-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {configs.general && Object.entries(configs.general.configs).map(([key, config]) => (
                        <div key={key} className="space-y-2">
                          <Label htmlFor={key}>
                            {config.description}
                            {config.required && <span className="text-red-500 ml-1">*</span>}
                          </Label>
                          {key === 'site_description' ? (
                            <Textarea
                              id={key}
                              value={configChanges[key] ?? config.value}
                              onChange={(e) => handleConfigChange(key, e.target.value)}
                              placeholder={config.description}
                              rows={3}
                            />
                          ) : (
                            <Input
                              id={key}
                              type={key === 'contact_email' ? 'email' : 'text'}
                              value={configChanges[key] ?? config.value}
                              onChange={(e) => handleConfigChange(key, e.target.value)}
                              placeholder={config.description}
                              required={config.required}
                            />
                          )}
                          {configChanges[key] && configChanges[key] !== config.value && (
                            <p className="text-xs text-blue-600">已修改</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </TabsContent>

                  {/* 邮件配置 */}
                  <TabsContent value="email" className="space-y-6 mt-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {configs.email && Object.entries(configs.email.configs).map(([key, config]) => (
                        <div key={key} className="space-y-2">
                          <Label htmlFor={key}>
                            {config.description}
                            {config.required && <span className="text-red-500 ml-1">*</span>}
                          </Label>
                          {config.sensitive ? (
                            <Input
                              id={key}
                              type="password"
                              value={configChanges[key] ?? (config.value === '******' ? '' : config.value)}
                              onChange={(e) => handleConfigChange(key, e.target.value)}
                              placeholder={config.value === '******' ? '输入新密码' : config.description}
                              required={config.required}
                            />
                          ) : key === 'smtp_port' ? (
                            <Input
                              id={key}
                              type="number"
                              min="1"
                              max="65535"
                              value={configChanges[key] ?? config.value}
                              onChange={(e) => handleConfigChange(key, e.target.value)}
                              placeholder={config.description}
                              required={config.required}
                            />
                          ) : key === 'smtp_use_tls' || key === 'smtp_use_ssl' ? (
                            <Select
                              value={configChanges[key] ?? config.value}
                              onValueChange={(value) => handleConfigChange(key, value)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="选择..." />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="true">启用</SelectItem>
                                <SelectItem value="false">禁用</SelectItem>
                              </SelectContent>
                            </Select>
                          ) : (
                            <Input
                              id={key}
                              type={key.includes('email') ? 'email' : 'text'}
                              value={configChanges[key] ?? config.value}
                              onChange={(e) => handleConfigChange(key, e.target.value)}
                              placeholder={config.description}
                              required={config.required}
                            />
                          )}
                          {configChanges[key] && configChanges[key] !== config.value && (
                            <p className="text-xs text-blue-600">已修改</p>
                          )}
                        </div>
                      ))}
                    </div>

                    {/* SMTP测试工具 */}
                    <div className="border-t pt-6">
                      <h4 className="font-medium mb-4">SMTP测试工具</h4>
                      <div className="flex gap-2 mb-4">
                        <Button
                          variant="outline"
                          onClick={testSMTPConnection}
                          disabled={smtpTesting}
                        >
                          {smtpTesting ? '测试中...' : '测试SMTP连接'}
                        </Button>
                        <Button
                          variant="outline"
                          onClick={sendTestEmail}
                          disabled={emailTesting}
                        >
                          {emailTesting ? '发送中...' : '发送测试邮件'}
                        </Button>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        建议先保存配置，然后测试SMTP连接和发送测试邮件验证配置是否正确。
                      </p>
                    </div>
                  </TabsContent>

                  {/* 用户管理配置 */}
                  <TabsContent value="users" className="space-y-4 mt-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {configs.users && Object.entries(configs.users.configs).map(([key, config]) => (
                        <div key={key} className="space-y-2">
                          <Label htmlFor={key}>
                            {config.description}
                            {config.required && <span className="text-red-500 ml-1">*</span>}
                          </Label>
                          {key === 'allow_registration' || key === 'require_email_verification' ? (
                            <Select
                              value={configChanges[key] ?? config.value}
                              onValueChange={(value) => handleConfigChange(key, value)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="选择..." />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="true">启用</SelectItem>
                                <SelectItem value="false">禁用</SelectItem>
                              </SelectContent>
                            </Select>
                          ) : key === 'default_user_role' ? (
                            <Select
                              value={configChanges[key] ?? config.value}
                              onValueChange={(value) => handleConfigChange(key, value)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="选择默认角色" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="user">普通用户</SelectItem>
                                <SelectItem value="member">会员</SelectItem>
                                <SelectItem value="vip">VIP用户</SelectItem>
                              </SelectContent>
                            </Select>
                          ) : key === 'max_favorite_count' ? (
                            <Input
                              id={key}
                              type="number"
                              min="0"
                              value={configChanges[key] ?? config.value}
                              onChange={(e) => handleConfigChange(key, e.target.value)}
                              placeholder={config.description}
                            />
                          ) : (
                            <Input
                              id={key}
                              value={configChanges[key] ?? config.value}
                              onChange={(e) => handleConfigChange(key, e.target.value)}
                              placeholder={config.description}
                              required={config.required}
                            />
                          )}
                          {configChanges[key] && configChanges[key] !== config.value && (
                            <p className="text-xs text-blue-600">已修改</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </TabsContent>

                  {/* 播放器配置 */}
                  <TabsContent value="player" className="space-y-6 mt-6">
                    <div className="space-y-6">
                      {/* 基本播放器设置 */}
                      <div>
                        <h4 className="font-medium mb-4">基本设置</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {configs.player && Object.entries(configs.player.configs)
                            .filter(([key]) => ['player_autoplay', 'player_theme_color', 'player_enable_rightclick', 'player_show_stats', 'player_show_version'].includes(key))
                            .map(([key, config]) => (
                            <div key={key} className="space-y-2">
                              <Label htmlFor={key}>
                                {config.description}
                                {config.required && <span className="text-red-500 ml-1">*</span>}
                              </Label>
                              {key === 'player_autoplay' || key === 'player_enable_rightclick' || key === 'player_show_stats' || key === 'player_show_version' ? (
                                <Select
                                  value={configChanges[key] ?? config.value}
                                  onValueChange={(value) => handleConfigChange(key, value)}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="选择..." />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="true">启用</SelectItem>
                                    <SelectItem value="false">禁用</SelectItem>
                                  </SelectContent>
                                </Select>
                              ) : key === 'player_theme_color' ? (
                                <div className="flex gap-2">
                                  <Input
                                    id={key}
                                    type="color"
                                    value={configChanges[key] ?? config.value}
                                    onChange={(e) => handleConfigChange(key, e.target.value)}
                                    className="w-20 h-10"
                                  />
                                  <Input
                                    value={configChanges[key] ?? config.value}
                                    onChange={(e) => handleConfigChange(key, e.target.value)}
                                    placeholder="#7c3aed"
                                    className="flex-1"
                                  />
                                </div>
                              ) : (
                                <Input
                                  id={key}
                                  value={configChanges[key] ?? config.value}
                                  onChange={(e) => handleConfigChange(key, e.target.value)}
                                  placeholder={config.description}
                                  required={config.required}
                                />
                              )}
                              {configChanges[key] && configChanges[key] !== config.value && (
                                <p className="text-xs text-blue-600">已修改</p>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* 广告配置 */}
                      <div className="border-t pt-6">
                        <h4 className="font-medium mb-4">广告配置</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {configs.player && Object.entries(configs.player.configs)
                            .filter(([key]) => ['player_enable_ads', 'player_preroll_ad_url', 'player_midroll_ad_url', 'player_postroll_ad_url', 'player_skip_ad_time', 'player_ad_volume'].includes(key))
                            .map(([key, config]) => (
                            <div key={key} className="space-y-2">
                              <Label htmlFor={key}>
                                {config.description}
                                {config.required && <span className="text-red-500 ml-1">*</span>}
                              </Label>
                              {key === 'player_enable_ads' ? (
                                <Select
                                  value={configChanges[key] ?? config.value}
                                  onValueChange={(value) => handleConfigChange(key, value)}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="选择..." />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="true">启用</SelectItem>
                                    <SelectItem value="false">禁用</SelectItem>
                                  </SelectContent>
                                </Select>
                              ) : key === 'player_skip_ad_time' ? (
                                <Input
                                  id={key}
                                  type="number"
                                  min="0"
                                  max="60"
                                  value={configChanges[key] ?? config.value}
                                  onChange={(e) => handleConfigChange(key, e.target.value)}
                                  placeholder="秒"
                                />
                              ) : key === 'player_ad_volume' ? (
                                <Input
                                  id={key}
                                  type="number"
                                  min="0"
                                  max="1"
                                  step="0.1"
                                  value={configChanges[key] ?? config.value}
                                  onChange={(e) => handleConfigChange(key, e.target.value)}
                                  placeholder="0.0 - 1.0"
                                />
                              ) : (
                                <Input
                                  id={key}
                                  type="url"
                                  value={configChanges[key] ?? config.value}
                                  onChange={(e) => handleConfigChange(key, e.target.value)}
                                  placeholder="https://example.com/ad.mp4"
                                />
                              )}
                              {configChanges[key] && configChanges[key] !== config.value && (
                                <p className="text-xs text-blue-600">已修改</p>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* VAST广告配置 */}
                      <div className="border-t pt-6">
                        <h4 className="font-medium mb-4">VAST广告配置</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {configs.player && Object.entries(configs.player.configs)
                            .filter(([key]) => ['player_enable_vast', 'player_vast_url'].includes(key))
                            .map(([key, config]) => (
                            <div key={key} className="space-y-2">
                              <Label htmlFor={key}>
                                {config.description}
                                {config.required && <span className="text-red-500 ml-1">*</span>}
                              </Label>
                              {key === 'player_enable_vast' ? (
                                <Select
                                  value={configChanges[key] ?? config.value}
                                  onValueChange={(value) => handleConfigChange(key, value)}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="选择..." />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="true">启用</SelectItem>
                                    <SelectItem value="false">禁用</SelectItem>
                                  </SelectContent>
                                </Select>
                              ) : (
                                <Input
                                  id={key}
                                  type="url"
                                  value={configChanges[key] ?? config.value}
                                  onChange={(e) => handleConfigChange(key, e.target.value)}
                                  placeholder="https://example.com/vast.xml"
                                />
                              )}
                              {configChanges[key] && configChanges[key] !== config.value && (
                                <p className="text-xs text-blue-600">已修改</p>
                              )}
                            </div>
                          ))}
                        </div>
                        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                          <h5 className="font-medium text-blue-900 mb-2">VAST广告说明</h5>
                          <p className="text-sm text-blue-700">
                            VAST (Video Ad Serving Template) 是一个视频广告标准，允许播放器与广告服务器通信。
                            输入VAST XML文件的URL地址，播放器将自动加载和显示广告。
                          </p>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">配置加载失败</p>
                  <Button variant="outline" onClick={fetchConfigs} className="mt-2">
                    重新加载
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}