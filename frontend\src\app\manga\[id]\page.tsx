'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Manga, MangaChapter, apiClient, Comment as CommentType, User } from '@/lib/api';
import { 
  Heart, 
  Eye, 
  Calendar, 
  User as UserIcon, 
  Bookmark, 
  Share2, 
  ArrowLeft,
  Play,
  Clock,
  Star,
  Copy,
  Check,
  ExternalLink,
  Twitter,
  Facebook,
  Reply,
  Quote,
  ThumbsUp,
  ThumbsDown,
  Edit2,
  Trash2,
  Save,
  X,
  ChevronDown,
  ChevronUp,
  ArrowUpDown,
  Grid3X3,
  List,
  ExpandIcon
} from 'lucide-react';
import Link from 'next/link';
import SafeImage from '@/components/ui/safe-image';
import { useAuth } from '@/contexts/AuthContext';

export default function MangaDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [manga, setManga] = useState<Manga | null>(null);
  const [chapters, setChapters] = useState<MangaChapter[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFavorited, setIsFavorited] = useState(false);
  const [expandDescription, setExpandDescription] = useState(false);
  const [shareMenuOpen, setShareMenuOpen] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  
  // 章节列表状态
  const [chapterSortOrder, setChapterSortOrder] = useState<'asc' | 'desc'>('desc'); // desc = 最新章节在前
  const [chapterDisplayMode, setChapterDisplayMode] = useState<'list' | 'grid'>('list'); // list = 一行一个，grid = 一行多个
  const [chaptersExpanded, setChaptersExpanded] = useState(false);

  const mangaId = Array.isArray(params.id) ? params.id[0] : params.id;

  useEffect(() => {
    if (mangaId) {
      fetchMangaDetail();
      fetchChapters();
      checkFavoriteStatus();
    }
  }, [mangaId]);

  const fetchMangaDetail = async () => {
    try {
      setLoading(true);
      const response = await apiClient.getManga(parseInt(mangaId as string));
      setManga(response);
    } catch (error) {
      console.error('Failed to fetch manga detail:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchChapters = async () => {
    try {
      const response = await apiClient.getMangaChapters(parseInt(mangaId as string));
      setChapters(response.chapters);
    } catch (error) {
      console.error('Failed to fetch chapters:', error);
      setChapters([]);
    }
  };

  const checkFavoriteStatus = async () => {
    if (!user) return;
    
    try {
      const response = await apiClient.checkFavorite('manga', parseInt(mangaId as string));
      setIsFavorited(response.is_favorited);
    } catch (error) {
      console.error('Failed to check favorite status:', error);
    }
  };

  const handleToggleFavorite = async () => {
    if (!user) {
      router.push('/auth');
      return;
    }

    const originalFavoriteState = isFavorited;
    const originalFavoriteCount = manga?.favorite_count || 0;

    try {
      // 乐观更新UI
      setIsFavorited(!isFavorited);
      if (manga) {
        setManga({
          ...manga,
          favorite_count: isFavorited 
            ? Math.max(0, manga.favorite_count - 1)
            : manga.favorite_count + 1
        });
      }

      if (isFavorited) {
        // 取消收藏
        await apiClient.removeFavorite('manga', parseInt(mangaId as string));
      } else {
        // 添加收藏
        await apiClient.addFavorite('manga', parseInt(mangaId as string));
      }
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
      
      // 恢复原始状态
      setIsFavorited(originalFavoriteState);
      if (manga) {
        setManga({
          ...manga,
          favorite_count: originalFavoriteCount
        });
      }
      
      // 显示用户友好的错误消息
      const errorMessage = error instanceof Error ? error.message : '操作失败，请重试';
      alert(errorMessage);
    }
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      setCopySuccess(true);
      setTimeout(() => {
        setCopySuccess(false);
        setShareMenuOpen(false);
      }, 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
      // 降级处理：使用传统方式复制
      const textArea = document.createElement('textarea');
      textArea.value = window.location.href;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopySuccess(true);
      setTimeout(() => {
        setCopySuccess(false);
        setShareMenuOpen(false);
      }, 2000);
    }
  };

  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: manga?.title,
          text: manga?.description,
          url: window.location.href,
        });
        setShareMenuOpen(false);
      } catch (error) {
        console.error('Failed to share:', error);
      }
    } else {
      // 如果不支持Web Share API，复制链接
      handleCopyLink();
    }
  };

  const handleSocialShare = (platform: 'twitter' | 'facebook') => {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent(`${manga?.title} - ${manga?.description?.slice(0, 100)}...`);
    
    let shareUrl = '';
    
    if (platform === 'twitter') {
      shareUrl = `https://twitter.com/intent/tweet?text=${text}&url=${url}`;
    } else if (platform === 'facebook') {
      shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
    }
    
    window.open(shareUrl, '_blank', 'width=600,height=400');
    setShareMenuOpen(false);
  };

  // 点击外部关闭分享菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (shareMenuOpen) {
        setShareMenuOpen(false);
      }
    };

    if (shareMenuOpen) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [shareMenuOpen]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      ongoing: '连载中',
      completed: '已完结',
      hiatus: '休载',
      cancelled: '已取消'
    };
    return statusMap[status] || status;
  };

  const getTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      serial: '连载',
      tankoubon: '单行本',
      doujinshi: '同人志'
    };
    return typeMap[type] || type;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ongoing':
        return 'bg-green-500';
      case 'completed':
        return 'bg-blue-500';
      case 'hiatus':
        return 'bg-yellow-500';
      case 'cancelled':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  // 章节排序处理
  const getSortedChapters = (chapters: MangaChapter[]) => {
    const sorted = [...chapters].sort((a, b) => {
      if (chapterSortOrder === 'desc') {
        return b.chapter_number - a.chapter_number; // 最新在前
      } else {
        return a.chapter_number - b.chapter_number; // 最旧在前
      }
    });
    return sorted;
  };

  // 显示的章节列表（考虑展开状态）
  const getDisplayedChapters = () => {
    const sorted = getSortedChapters(chapters);
    if (chaptersExpanded) {
      return sorted;
    }
    return sorted.slice(0, 12); // 未展开时只显示前12个
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="text-lg">加载中...</div>
        </div>
      </div>
    );
  }

  if (!manga) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="text-lg">漫画不存在</div>
          <Button onClick={() => router.push('/manga')} className="mt-4">
            返回漫画列表
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* 返回按钮 */}
      <Button 
        variant="ghost" 
        onClick={() => router.push('/manga')}
        className="mb-4"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        返回漫画列表
      </Button>

      {/* 漫画信息 */}
      <div className="bg-card border rounded-lg overflow-hidden">
        <div className="md:flex">
          {/* 封面 */}
          <div className="md:w-80 md:flex-shrink-0">
            <div className="aspect-[3/4] md:aspect-auto md:h-96 relative">
              <SafeImage
                src={manga.cover || '/placeholder-manga.jpg'}
                alt={manga.title}
                fill
                className="object-cover"
                priority
              />
            </div>
          </div>

          {/* 基本信息 */}
          <div className="flex-1 p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1 min-w-0">
                <h1 className="text-2xl md:text-3xl font-bold mb-2">{manga.title}</h1>
                {manga.title_original && (
                  <p className="text-lg text-muted-foreground mb-2">
                    {manga.title_original}
                  </p>
                )}
                
                <div className="flex flex-wrap gap-2 mb-4">
                  <Badge 
                    className={`${getStatusColor(manga.status)} text-white`}
                  >
                    {getStatusText(manga.status)}
                  </Badge>
                  {manga.category && (
                    <Badge variant="secondary">
                      {manga.category.name}
                    </Badge>
                  )}
                </div>
                
                {/* 统计信息 - 紧凑版 */}
                <div className="flex items-center gap-4 mb-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Eye className="h-4 w-4" />
                    <span className="font-medium">{manga.view_count}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Heart className="h-4 w-4" />
                    <span className="font-medium">{manga.favorite_count}</span>
                  </div>
                </div>
                
                {/* 标签区域 */}
                {manga.tags && manga.tags.length > 0 && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-muted-foreground mb-2">标签</h4>
                    <div className="flex flex-wrap gap-2">
                      {manga.tags.map((tag) => (
                        <Badge key={tag.id} variant="outline" className="text-xs">
                          {tag.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex flex-wrap gap-3 mb-6">
              {chapters.length > 0 && (
                <Link href={`/manga/${manga.id}/read/${chapters[0].chapter_number}`}>
                  <Button size="lg" className="flex items-center gap-2">
                    <Play className="h-4 w-4" />
                    开始阅读
                  </Button>
                </Link>
              )}
              <Button 
                variant={isFavorited ? "default" : "outline"} 
                onClick={handleToggleFavorite}
                className="flex items-center gap-2"
              >
                <Heart className={`h-4 w-4 ${isFavorited ? 'fill-current' : ''}`} />
                {isFavorited ? '已收藏' : '收藏'}
              </Button>
              <div className="relative">
                <Button 
                  variant="outline" 
                  onClick={() => setShareMenuOpen(!shareMenuOpen)}
                >
                  <Share2 className="h-4 w-4 mr-2" />
                  分享
                </Button>
                
                {shareMenuOpen && (
                  <div className="absolute top-full left-0 mt-2 w-48 bg-card border rounded-lg shadow-lg z-50">
                    <div className="p-2 space-y-1">
                      <Button
                        variant="ghost"
                        className="w-full justify-start text-sm h-8"
                        onClick={handleCopyLink}
                      >
                        {copySuccess ? (
                          <>
                            <Check className="h-4 w-4 mr-2" />
                            已复制
                          </>
                        ) : (
                          <>
                            <Copy className="h-4 w-4 mr-2" />
                            复制链接
                          </>
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        className="w-full justify-start text-sm h-8"
                        onClick={handleNativeShare}
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        系统分享
                      </Button>
                      <Button
                        variant="ghost"
                        className="w-full justify-start text-sm h-8"
                        onClick={() => handleSocialShare('twitter')}
                      >
                        <Twitter className="h-4 w-4 mr-2" />
                        分享到 Twitter
                      </Button>
                      <Button
                        variant="ghost"
                        className="w-full justify-start text-sm h-8"
                        onClick={() => handleSocialShare('facebook')}
                      >
                        <Facebook className="h-4 w-4 mr-2" />
                        分享到 Facebook
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 详细信息 */}
            <div className="space-y-3 text-sm">
              {manga.author && (
                <div className="flex items-center gap-2">
                  <UserIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">作者:</span>
                  <span>{manga.author}</span>
                </div>
              )}
              {manga.artist && manga.artist !== manga.author && (
                <div className="flex items-center gap-2">
                  <UserIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">画师:</span>
                  <span>{manga.artist}</span>
                </div>
              )}
              {manga.release_date && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">发布:</span>
                  <span>{formatDate(manga.release_date)}</span>
                </div>
              )}
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">更新:</span>
                <span>{formatDate(manga.updated_at)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* 简介 */}
        {manga.description && (
          <div className="px-6 pb-6">
            <Separator className="mb-4" />
            <div>
              <h3 className="font-semibold mb-3">简介</h3>
              <div className={`text-muted-foreground ${!expandDescription ? 'line-clamp-3' : ''}`}>
                {manga.description}
              </div>
              {manga.description.length > 150 && (
                <Button 
                  variant="link" 
                  className="p-0 h-auto mt-2"
                  onClick={() => setExpandDescription(!expandDescription)}
                >
                  {expandDescription ? '收起' : '展开'}
                </Button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 章节列表 */}
      <div className="bg-card border rounded-lg">
        <div className="p-6 border-b">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-xl font-semibold">章节列表</h2>
            <div className="flex items-center gap-2">
              {/* 排序按钮 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setChapterSortOrder(prev => prev === 'desc' ? 'asc' : 'desc')}
                className="flex items-center gap-1"
              >
                <ArrowUpDown className="h-4 w-4" />
                {chapterSortOrder === 'desc' ? '最新优先' : '最旧优先'}
              </Button>
              
              {/* 显示模式切换 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setChapterDisplayMode(prev => prev === 'list' ? 'grid' : 'list')}
                className="flex items-center gap-1"
              >
                {chapterDisplayMode === 'list' ? (
                  <>
                    <Grid3X3 className="h-4 w-4" />
                    网格
                  </>
                ) : (
                  <>
                    <List className="h-4 w-4" />
                    列表
                  </>
                )}
              </Button>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <p className="text-muted-foreground text-sm">
              共 {chapters.length} 章 {!chaptersExpanded && chapters.length > 12 && `(显示前 ${Math.min(12, chapters.length)} 章)`}
            </p>
            {chapters.length > 12 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setChaptersExpanded(!chaptersExpanded)}
                className="flex items-center gap-1"
              >
                <ExpandIcon className="h-4 w-4" />
                {chaptersExpanded ? '收起' : '展开全部'}
              </Button>
            )}
          </div>
        </div>
        
        <div className="p-6">
          {chapters.length > 0 ? (
            chapterDisplayMode === 'list' ? (
              // 列表模式 - 一行一个章节
              <div className="space-y-2">
                {getDisplayedChapters().map((chapter, index) => (
                  <Link 
                    key={chapter.id}
                    href={`/manga/${manga.id}/read/${chapter.chapter_number}`}
                    className="block hover:bg-muted/50 rounded-lg transition-colors"
                  >
                    <div className="p-4 flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-3">
                          <span className="text-sm text-muted-foreground w-12">
                            #{chapterSortOrder === 'desc' ? chapter.chapter_number : chapters.length - index}
                          </span>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium truncate">
                              第 {chapter.chapter_number} 话
                            </h4>
                            {chapter.title && (
                              <p className="text-sm text-muted-foreground truncate">
                                {chapter.title}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground ml-4">
                        {formatDate(chapter.created_at)}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              // 网格模式 - 一行多个章节
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-3">
                {getDisplayedChapters().map((chapter) => (
                  <Link 
                    key={chapter.id}
                    href={`/manga/${manga.id}/read/${chapter.chapter_number}`}
                    className="block hover:bg-muted/50 rounded-lg transition-colors"
                  >
                    <div className="p-3 text-center">
                      <div className="font-medium text-sm mb-1">
                        第 {chapter.chapter_number} 话
                      </div>
                      {chapter.title && (
                        <div className="text-xs text-muted-foreground line-clamp-2">
                          {chapter.title}
                        </div>
                      )}
                    </div>
                  </Link>
                ))}
              </div>
            )
          ) : (
            <div className="p-8 text-center text-muted-foreground">
              暂无章节
            </div>
          )}
          
          {/* 展开更多提示 */}
          {!chaptersExpanded && chapters.length > 12 && (
            <div className="mt-4 text-center">
              <Button
                variant="outline"
                onClick={() => setChaptersExpanded(true)}
                className="flex items-center gap-2"
              >
                <ChevronDown className="h-4 w-4" />
                查看剩余 {chapters.length - 12} 个章节
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* 相关推荐 */}
      <RelatedMangaRecommendations mangaId={parseInt(mangaId as string)} />

      {/* 评论区 */}
      <MangaCommentSection mangaId={parseInt(mangaId as string)} />
    </div>
  );
}

// 相关推荐组件
function RelatedMangaRecommendations({ mangaId }: { mangaId: number }) {
  const [relatedMangas, setRelatedMangas] = useState<Manga[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRelatedMangas();
  }, [mangaId]);

  const fetchRelatedMangas = async () => {
    try {
      setLoading(true);
      const response = await apiClient.getRelatedMangas(mangaId, 8);
      setRelatedMangas(response);
    } catch (error) {
      console.error('Failed to fetch related mangas:', error);
      setRelatedMangas([]);
    } finally {
      setLoading(false);
    }
  };

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      ongoing: '连载中',
      completed: '已完结',
      hiatus: '休载',
      cancelled: '已取消'
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ongoing':
        return 'bg-green-500';
      case 'completed':
        return 'bg-blue-500';
      case 'hiatus':
        return 'bg-yellow-500';
      case 'cancelled':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  if (loading) {
    return (
      <div className="bg-card border rounded-lg">
        <div className="p-6 border-b">
          <h2 className="text-xl font-semibold">相关推荐</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="aspect-[3/4] bg-muted rounded-lg animate-pulse" />
                <div className="h-4 bg-muted rounded animate-pulse" />
                <div className="h-3 bg-muted rounded animate-pulse w-3/4" />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (relatedMangas.length === 0) {
    return null;
  }

  return (
    <div className="bg-card border rounded-lg">
      <div className="p-6 border-b">
        <h2 className="text-xl font-semibold">相关推荐</h2>
        <p className="text-muted-foreground text-sm mt-1">
          基于分类和标签的智能推荐
        </p>
      </div>
      <div className="p-6">
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
          {relatedMangas.map((manga) => (
            <Link
              key={manga.id}
              href={`/manga/${manga.id}`}
              className="group block space-y-2 hover:scale-105 transition-transform"
            >
              <div className="aspect-[3/4] relative rounded-lg overflow-hidden bg-muted">
                <SafeImage
                  src={manga.cover || '/placeholder-manga.jpg'}
                  alt={manga.title}
                  fill
                  className="object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute top-2 left-2">
                  <Badge 
                    className={`${getStatusColor(manga.status)} text-white text-xs`}
                  >
                    {getStatusText(manga.status)}
                  </Badge>
                </div>
              </div>
              <div className="space-y-1">
                <h3 className="font-medium text-sm line-clamp-1 group-hover:text-primary transition-colors">
                  {manga.title}
                </h3>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    <span>{manga.view_count}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Heart className="h-3 w-3" />
                    <span>{manga.favorite_count}</span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}

// 漫画评论组件
function MangaCommentSection({ mangaId }: { mangaId: number }) {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const [comments, setComments] = useState<CommentType[]>([]);
  const [commentLoading, setCommentLoading] = useState<boolean>(false);
  const [newComment, setNewComment] = useState<string>('');
  const [attachments, setAttachments] = useState<string[]>([]);
  const [posting, setPosting] = useState<boolean>(false);
  
  // 回复和引用状态
  const [replyToComment, setReplyToComment] = useState<CommentType | null>(null);
  const [quotedComment, setQuotedComment] = useState<CommentType | null>(null);
  const [editingComment, setEditingComment] = useState<number | null>(null);
  const [editContent, setEditContent] = useState('');
  
  // 收缩展开状态管理
  const [collapsedReplies, setCollapsedReplies] = useState<Set<number>>(new Set());
  
  // 评论分页状态
  const [commentPage, setCommentPage] = useState(1);
  const [totalComments, setTotalComments] = useState(0);
  const [loadingMore, setLoadingMore] = useState(false);
  const commentsPerPage = 20;

  useEffect(() => {
    if (mangaId) {
      fetchComments();
    }
  }, [mangaId]);

  const fetchComments = async (page: number = 1, append: boolean = false) => {
    try {
      if (!append) setCommentLoading(true);
      else setLoadingMore(true);
      
      const skip = (page - 1) * commentsPerPage;
      const response = await apiClient.listMangaCommentsWithReplies(mangaId, {
        skip,
        limit: commentsPerPage,
      });
      
      if (append) {
        setComments(prev => [...prev, ...response]);
      } else {
        setComments(response);
      }
      
      setTotalComments(Math.max(totalComments, response.length + skip));
      setCommentPage(page);
    } catch (error) {
      console.error('Failed to fetch comments:', error);
      if (!append) setComments([]);
    } finally {
      setCommentLoading(false);
      setLoadingMore(false);
    }
  };

  const loadMoreComments = () => {
    fetchComments(commentPage + 1, true);
  };

  const addEmoji = (emoji: string) => {
    setNewComment(prev => prev + emoji);
  };

  const addAttachment = () => {
    const imageUrl = prompt('请输入图片 URL:');
    if (imageUrl && imageUrl.trim()) {
      setAttachments(prev => [...prev, imageUrl.trim()]);
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const handleReply = (comment: CommentType) => {
    setReplyToComment(comment);
    setQuotedComment(null); // 回复时清除引用
  };

  const handleQuote = (comment: CommentType) => {
    setQuotedComment(comment);
    setReplyToComment(null); // 引用时清除回复
    // 自动在输入框中添加引用内容
    const quotedText = `> ${comment.user?.username || '匿名用户'}: ${comment.content}\n\n`;
    setNewComment(quotedText);
  };

  const cancelReplyOrQuote = () => {
    setReplyToComment(null);
    setQuotedComment(null);
    setNewComment('');
    setEditingComment(null);
    setEditContent('');
  };

  // 编辑评论处理函数
  const handleEdit = (comment: CommentType) => {
    setEditingComment(comment.id);
    setEditContent(comment.content);
  };

  const cancelEdit = () => {
    setEditingComment(null);
    setEditContent('');
  };

  const submitEdit = async () => {
    if (!editingComment || !editContent.trim()) return;

    try {
      const updatedComment = await apiClient.updateComment(editingComment, editContent.trim());
      
      // 更新本地评论列表
      setComments(prevComments => 
        updateCommentInList(prevComments, updatedComment.id, updatedComment)
      );
      
      setEditingComment(null);
      setEditContent('');
    } catch (error) {
      console.error('Failed to update comment:', error);
    }
  };

  // 点赞/反对处理函数
  const handleLike = async (commentId: number, isLike: boolean) => {
    try {
      const result = await apiClient.toggleCommentLike(commentId, isLike);
      
      // 更新本地评论数据
      setComments(prevComments => 
        updateCommentInList(prevComments, commentId, {
          like_count: result.like_count,
          dislike_count: result.dislike_count,
          user_like_status: result.user_like_status
        })
      );
    } catch (error) {
      console.error('Failed to toggle like:', error);
    }
  };

  // 删除评论处理函数
  const handleDelete = async (commentId: number) => {
    if (!confirm('确定要删除这条评论吗？')) return;

    try {
      await apiClient.deleteComment(commentId);
      
      // 从本地评论列表中移除
      setComments(prevComments => removeCommentFromList(prevComments, commentId));
    } catch (error) {
      console.error('Failed to delete comment:', error);
    }
  };

  // 辅助函数：更新评论列表中的特定评论
  const updateCommentInList = (comments: CommentType[], commentId: number, updates: Partial<CommentType>): CommentType[] => {
    return comments.map(comment => {
      if (comment.id === commentId) {
        return { ...comment, ...updates };
      }
      if (comment.replies) {
        return { ...comment, replies: updateCommentInList(comment.replies, commentId, updates) };
      }
      return comment;
    });
  };

  // 辅助函数：从评论列表中删除特定评论
  const removeCommentFromList = (comments: CommentType[], commentId: number): CommentType[] => {
    return comments.filter(comment => {
      if (comment.id === commentId) {
        return false; // 删除该评论
      }
      if (comment.replies) {
        comment.replies = removeCommentFromList(comment.replies, commentId);
      }
      return true;
    });
  };

  // 切换回复收缩展开状态
  const toggleRepliesCollapse = (commentId: number) => {
    setCollapsedReplies(prev => {
      const newSet = new Set(prev);
      if (newSet.has(commentId)) {
        newSet.delete(commentId);
      } else {
        newSet.add(commentId);
      }
      return newSet;
    });
  };

  const submitComment = async () => {
    if (!isAuthenticated) {
      router.push('/auth');
      return;
    }
    const content = newComment.trim();
    if (!content) return;
    setPosting(true);
    try {
      const commentData: {
        manga_id: number;
        content: string;
        attachments?: string[];
        parent_id?: number;
        reply_to_user_id?: number;
        quoted_comment_id?: number;
      } = {
        manga_id: mangaId,
        content,
        attachments: attachments.length ? attachments : undefined,
      };

      // 添加回复参数
      if (replyToComment) {
        commentData.parent_id = replyToComment.id;
        commentData.reply_to_user_id = replyToComment.user?.id;
      }

      // 添加引用参数
      if (quotedComment) {
        commentData.quoted_comment_id = quotedComment.id;
      }

      await apiClient.createMangaComment(commentData);
      setNewComment('');
      setAttachments([]);
      setReplyToComment(null);
      setQuotedComment(null);
      // 重置分页并重新获取评论
      setCommentPage(1);
      await fetchComments(1, false);
    } catch (error) {
      console.error('Failed to post comment:', error);
    } finally {
      setPosting(false);
    }
  };

  // 递归渲染评论的函数
  const renderComment = (comment: CommentType, depth: number = 0): React.ReactNode => {
    const maxDepth = 2; // 最大嵌套深度
    const indent = depth * 20; // 缩进像素
    
    return (
      <div key={comment.id} style={{ marginLeft: `${indent}px` }} className="space-y-3">
        <div className="border-l-2 border-gray-200 pl-3">
          {/* 引用的评论显示 */}
          {comment.quoted_comment_id && (
            <div className="mb-2 p-2 bg-gray-50 rounded border-l-4 border-blue-300">
              <div className="text-xs text-gray-500 mb-1">
                引用了 @{comment.quoted_comment?.user?.username || '匿名用户'} 的评论:
              </div>
              <div className="text-sm text-gray-600 line-clamp-2">
                {comment.quoted_comment?.content || '原评论已删除'}
              </div>
            </div>
          )}
          
          {/* 评论主体 */}
          <div className="flex gap-2 md:gap-3">
            <div className="flex-shrink-0">
              <div className="w-6 h-6 md:w-8 md:h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white text-xs md:text-sm font-bold">
                {comment.user?.username?.[0]?.toUpperCase() || 'A'}
              </div>
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-semibold text-sm md:text-base">
                  {comment.user?.username || '匿名用户'}
                </span>
                <span className="text-xs text-muted-foreground">
                  {new Date(comment.created_at).toLocaleString('zh-CN', {
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
                {/* 显示编辑标识 */}
                {comment.is_edited && (
                  <span className="text-xs text-gray-500 italic">(已编辑)</span>
                )}
              </div>
              
              {/* 评论内容区域 */}
              {editingComment === comment.id ? (
                <div className="mb-2">
                  <textarea
                    value={editContent}
                    onChange={(e) => setEditContent(e.target.value)}
                    className="w-full p-2 border rounded-md text-sm resize-none"
                    rows={3}
                    placeholder="编辑评论内容..."
                    maxLength={2000}
                  />
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-xs text-gray-500">
                      {editContent.length}/2000
                    </span>
                    <div className="flex gap-2">
                      <button
                        onClick={async () => {
                          if (editContent.trim() && editContent.trim() !== comment.content) {
                            try {
                              const updatedComment = await apiClient.updateComment(comment.id, editContent.trim());
                              // 更新评论列表中的评论
                              setComments(prevComments => 
                                updateCommentInList(prevComments, comment.id, {
                                  ...updatedComment,
                                  user_like_status: comment.user_like_status
                                })
                              );
                              setEditingComment(null);
                              setEditContent('');
                            } catch (error) {
                              console.error('编辑评论失败:', error);
                              alert('编辑失败，请重试');
                            }
                          }
                        }}
                        className="flex items-center gap-1 px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
                        disabled={!editContent.trim() || editContent.trim() === comment.content}
                      >
                        <Save className="w-3 h-3" />
                        保存
                      </button>
                      <button
                        onClick={() => {
                          setEditingComment(null);
                          setEditContent('');
                        }}
                        className="flex items-center gap-1 px-3 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600 transition-colors"
                      >
                        <X className="w-3 h-3" />
                        取消
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-sm md:text-base text-foreground mb-2 break-words">
                  {comment.content}
                </div>
              )}
              
              {/* 附件显示 */}
              {comment.attachments && comment.attachments.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-2">
                  {comment.attachments.map((url, idx) => (
                    <img
                      key={idx}
                      src={url}
                      alt="attachment"
                      className="max-w-20 md:max-w-32 max-h-20 md:max-h-32 rounded object-cover cursor-pointer hover:opacity-80"
                      onClick={() => window.open(url, '_blank')}
                    />
                  ))}
                </div>
              )}
              
              {/* 操作按钮 */}
              <div className="flex items-center gap-3 text-xs">
                {/* 点赞/反对按钮 */}
                {isAuthenticated && (
                  <>
                    <button
                      onClick={() => handleLike(comment.id, true)}
                      className={`flex items-center gap-1 transition-colors ${
                        comment.user_like_status === true 
                          ? 'text-blue-500' 
                          : 'text-gray-500 hover:text-blue-500'
                      }`}
                    >
                      <ThumbsUp className="w-3 h-3" />
                      <span>{comment.like_count || 0}</span>
                    </button>
                    
                    <button
                      onClick={() => handleLike(comment.id, false)}
                      className={`flex items-center gap-1 transition-colors ${
                        comment.user_like_status === false 
                          ? 'text-red-500' 
                          : 'text-gray-500 hover:text-red-500'
                      }`}
                    >
                      <ThumbsDown className="w-3 h-3" />
                      <span>{comment.dislike_count || 0}</span>
                    </button>
                  </>
                )}
                
                <button
                  onClick={() => handleReply(comment)}
                  className="flex items-center gap-1 text-gray-500 hover:text-blue-500 transition-colors"
                  disabled={!isAuthenticated}
                >
                  <Reply className="w-3 h-3" />
                  <span>回复</span>
                </button>
                
                <button
                  onClick={() => handleQuote(comment)}
                  className="flex items-center gap-1 text-gray-500 hover:text-green-500 transition-colors"
                  disabled={!isAuthenticated}
                >
                  <Quote className="w-3 h-3" />
                  <span>引用</span>
                </button>
                
                {/* 编辑和删除按钮（只对自己的评论显示） */}
                {isAuthenticated && user && comment.user?.id === user.id && (
                  <>
                    <button
                      onClick={() => handleEdit(comment)}
                      className="flex items-center gap-1 text-gray-500 hover:text-orange-500 transition-colors"
                    >
                      <Edit2 className="w-3 h-3" />
                      <span>编辑</span>
                    </button>
                    
                    <button
                      onClick={() => handleDelete(comment.id)}
                      className="flex items-center gap-1 text-gray-500 hover:text-red-500 transition-colors"
                    >
                      <Trash2 className="w-3 h-3" />
                      <span>删除</span>
                    </button>
                  </>
                )}
              </div>
              
              {/* 回复收缩/展开控制按钮 */}
              {depth < maxDepth && comment.replies && comment.replies.length > 0 && (
                <div className="mt-2">
                  <button
                    onClick={() => toggleRepliesCollapse(comment.id)}
                    className="flex items-center gap-1 text-xs text-blue-500 hover:text-blue-700 transition-colors"
                  >
                    {collapsedReplies.has(comment.id) ? (
                      <>
                        <ChevronDown className="w-3 h-3" />
                        <span>查看 {comment.replies.length} 条回复</span>
                      </>
                    ) : (
                      <>
                        <ChevronUp className="w-3 h-3" />
                        <span>隐藏 {comment.replies.length} 条回复</span>
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* 回复列表渲染 */}
        {depth < maxDepth && comment.replies && comment.replies.length > 0 && !collapsedReplies.has(comment.id) && (
          <div className="space-y-3">
            {comment.replies.map((reply) => renderComment(reply, depth + 1))}
          </div>
        )}
        
        {/* 如果达到最大深度但还有回复，显示"查看更多回复"链接 */}
        {depth >= maxDepth && comment.replies && comment.replies.length > 0 && (
          <div className="text-xs text-blue-500 cursor-pointer hover:text-blue-700">
            查看 {comment.replies.length} 条回复...
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-card border rounded-lg">
      <div className="p-6 border-b">
        <h2 className="text-xl font-semibold">评论</h2>
        <p className="text-muted-foreground text-sm mt-1">
          分享你的阅读感受
        </p>
      </div>
      <div className="p-6 space-y-4">
        {/* New comment form */}
        <div className="space-y-2">
          {/* 回复/引用上下文显示 */}
          {(replyToComment || quotedComment) && (
            <div className="p-3 bg-gray-50 border rounded-md">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">
                  {replyToComment ? '正在回复:' : '正在引用:'}
                </span>
                <button
                  onClick={cancelReplyOrQuote}
                  className="text-gray-500 hover:text-gray-700 text-sm"
                >
                  取消
                </button>
              </div>
              <div className="text-sm text-gray-600">
                <strong>{(replyToComment || quotedComment)?.user?.username || '匿名用户'}：</strong>
                <span className="line-clamp-2">
                  {(replyToComment || quotedComment)?.content}
                </span>
              </div>
            </div>
          )}
          
          <textarea
            className="w-full min-h-[80px] md:min-h-[90px] p-2 md:p-3 rounded-md border bg-background text-sm md:text-base"
            placeholder={replyToComment ? "写下你的回复..." : quotedComment ? "引用并发表你的看法..." : "说点什么..."}
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
          />
          <div className="flex flex-wrap items-center gap-2">
            {/* Quick emoji bar */}
            <div className="flex flex-wrap gap-1">
              {['😀','😍','😎','😡','😭','👍','👎','🔥','💯'].map((emo) => (
                <button
                  key={emo}
                  type="button"
                  className="px-1 md:px-2 py-1 text-lg md:text-xl hover:opacity-80"
                  onClick={() => addEmoji(emo)}
                  title="插入表情"
                >
                  {emo}
                </button>
              ))}
            </div>
            <div className="flex flex-wrap gap-2 mt-2 md:mt-0">
              <Button type="button" variant="outline" size="sm" onClick={addAttachment}>
                <span className="hidden md:inline">添加表情包</span>
                <span className="md:hidden">表情包</span>
              </Button>
              <Button type="button" size="sm" onClick={submitComment} disabled={posting}>
                <span className="hidden md:inline">发表评论</span>
                <span className="md:hidden">发表</span>
              </Button>
            </div>
          </div>
          {/* Attachments preview */}
          {attachments.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-2">
              {attachments.map((url, idx) => (
                <div key={`${url}-${idx}`} className="relative">
                  <img src={url} alt="attachment" className="w-16 h-16 object-cover rounded" />
                  <button
                    className="absolute -top-2 -right-2 bg-black/70 text-white rounded-full w-6 h-6"
                    onClick={() => removeAttachment(idx)}
                    title="移除"
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Comment list */}
        <div className="space-y-4">
          {commentLoading ? (
            <div className="text-sm text-muted-foreground">评论加载中...</div>
          ) : comments.length === 0 ? (
            <div className="text-sm text-muted-foreground">还没有评论，来抢沙发吧～</div>
          ) : (
            <>
              {comments.map((comment) => renderComment(comment, 0))}
              
              {/* 加载更多按钮 */}
              {comments.length >= commentsPerPage && comments.length < totalComments && (
                <div className="flex justify-center pt-4">
                  <Button 
                    variant="outline" 
                    onClick={loadMoreComments}
                    disabled={loadingMore}
                    className="w-full md:w-auto"
                  >
                    {loadingMore ? '加载中...' : `加载更多评论 (已显示 ${comments.length}/${totalComments > comments.length ? '更多' : totalComments})`}
                  </Button>
                </div>
              )}
              
              {/* 显示评论统计 */}
              <div className="text-xs text-muted-foreground text-center pt-2">
                {totalComments > 0 && (
                  comments.length >= totalComments 
                    ? `共 ${totalComments} 条评论，已全部加载`
                    : `已显示 ${comments.length} 条评论，按最新发布时间排序`
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}