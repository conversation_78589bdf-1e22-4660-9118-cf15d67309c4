{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/line-clamp": "^0.4.4", "artplayer": "^5.1.3", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.539.0", "next": "^15.4.6", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.36.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/node": "^24.2.1", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "eslint": "^9", "eslint-config-next": "15.4.6", "jest": "^29.6.0", "jest-environment-jsdom": "^29.6.0", "msw": "^1.2.3", "tailwindcss": "^4.1.11", "typescript": "^5.9.2"}}