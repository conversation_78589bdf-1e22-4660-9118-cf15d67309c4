# 动漫网站手动部署指南

## 🛠️ 手动部署详细步骤

### 1. 服务器环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 安装Node.js (用于前端构建)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装Python (用于后端)
sudo apt install python3 python3-pip python3-venv -y
```

### 2. 项目文件清理

```bash
# 进入项目目录
cd anime-website

# 清理调试文件
find backend/claw -name "debug_*" -delete
find backend/manhua -name "debug_*" -delete

# 清理日志文件
find backend -name "*.log" -delete

# 清理临时文件
rm -rf backend/claw/temp/
rm -f backend/claw/temp_*.yml

# 清理压缩包
rm -f backend/app.zip frontend/src.zip

# 清理测试数据 (可选)
rm -f backend/comments_export.json

# 清理测试文件 (可选，建议保留备份)
# find backend -name "test_*.py" -delete
# rm -rf backend/tests/
# rm -rf frontend/src/components/auth/__tests__/
```

### 3. 环境配置

创建生产环境配置文件：

```bash
# 创建 .env.production
cat > .env.production << 'EOF'
# 数据库配置
DATABASE_URL=mysql://anime_user:YOUR_STRONG_PASSWORD@db:3306/anime_db
MYSQL_ROOT_PASSWORD=YOUR_SUPER_STRONG_ROOT_PASSWORD
MYSQL_DATABASE=anime_db
MYSQL_USER=anime_user
MYSQL_PASSWORD=YOUR_STRONG_PASSWORD

# JWT密钥 (生成32字符以上的随机字符串)
JWT_SECRET=your-production-jwt-secret-key-must-be-very-strong-and-random

# CORS设置
CORS_ORIGINS=["https://yourdomain.com","https://www.yourdomain.com"]

# 前端API地址
NEXT_PUBLIC_API_BASE_URL=https://yourdomain.com
NEXT_PUBLIC_DISABLE_DEVTOOLS=true
NEXT_PUBLIC_DISABLE_CONSOLE=true
NODE_ENV=production
EOF
```

### 4. 前端构建

```bash
cd frontend

# 安装依赖
npm ci

# 构建生产版本
npm run build

# 检查构建结果
ls -la .next/

cd ..
```

### 5. 后端准备

```bash
cd backend

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 检查数据库迁移
python -m alembic check

cd ..
```

### 6. 数据库设置

```bash
# 启动MySQL容器
docker run -d \
  --name anime-mysql \
  -e MYSQL_ROOT_PASSWORD=YOUR_SUPER_STRONG_ROOT_PASSWORD \
  -e MYSQL_DATABASE=anime_db \
  -e MYSQL_USER=anime_user \
  -e MYSQL_PASSWORD=YOUR_STRONG_PASSWORD \
  -p 3306:3306 \
  -v anime_mysql_data:/var/lib/mysql \
  mysql:8.0

# 等待数据库启动
sleep 30

# 运行数据库迁移
cd backend
source venv/bin/activate
python -m alembic upgrade head
cd ..
```

### 7. 应用启动

```bash
# 启动后端 (在后台运行)
cd backend
source venv/bin/activate
nohup uvicorn app.main:app --host 0.0.0.0 --port 8000 &
cd ..

# 启动前端 (在后台运行)
cd frontend
nohup npm start &
cd ..
```

### 8. Nginx反向代理配置

创建 Nginx 配置文件：

```bash
sudo cat > /etc/nginx/sites-available/anime-website << 'EOF'
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    
    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    
    # SSL 配置 (需要获取SSL证书)
    ssl_certificate /etc/ssl/certs/yourdomain.com.crt;
    ssl_certificate_key /etc/ssl/private/yourdomain.com.key;
    
    # 前端代理
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件缓存
    location /_next/static/ {
        proxy_pass http://localhost:3000;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# 启用站点
sudo ln -s /etc/nginx/sites-available/anime-website /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 9. SSL证书配置

```bash
# 使用Let's Encrypt获取免费SSL证书
sudo apt install certbot python3-certbot-nginx -y
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

### 10. 系统服务配置

创建systemd服务文件：

```bash
# 后端服务
sudo cat > /etc/systemd/system/anime-backend.service << 'EOF'
[Unit]
Description=Anime Website Backend
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/anime-website/backend
Environment=PATH=/path/to/anime-website/backend/venv/bin
ExecStart=/path/to/anime-website/backend/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000
Restart=on-failure

[Install]
WantedBy=multi-user.target
EOF

# 前端服务
sudo cat > /etc/systemd/system/anime-frontend.service << 'EOF'
[Unit]
Description=Anime Website Frontend
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/anime-website/frontend
ExecStart=/usr/bin/npm start
Restart=on-failure
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
EOF

# 启用服务
sudo systemctl daemon-reload
sudo systemctl enable anime-backend anime-frontend
sudo systemctl start anime-backend anime-frontend
```

### 11. 健康检查和监控

```bash
# 检查服务状态
sudo systemctl status anime-backend
sudo systemctl status anime-frontend

# 检查端口
netstat -tlnp | grep -E ':(3000|8000|3306)'

# 测试API
curl -I http://localhost:8000/api/v1/
curl -I http://localhost:3000/

# 查看日志
journalctl -u anime-backend -f
journalctl -u anime-frontend -f
```

## 🔒 安全加固

### 1. 防火墙配置

```bash
# 配置ufw防火墙
sudo ufw enable
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw deny 3000/tcp   # 禁止直接访问前端
sudo ufw deny 8000/tcp   # 禁止直接访问后端API
```

### 2. 数据库安全

```bash
# 运行MySQL安全脚本
docker exec -it anime-mysql mysql_secure_installation

# 创建数据库用户权限限制
docker exec -it anime-mysql mysql -u root -p << 'EOF'
GRANT SELECT, INSERT, UPDATE, DELETE ON anime_db.* TO 'anime_user'@'%';
FLUSH PRIVILEGES;
EOF
```

### 3. 应用安全

- 定期更新依赖包
- 使用强密码
- 配置HTTPS
- 限制API访问频率
- 配置CORS策略

## 📊 监控与维护

### 1. 日志监控

```bash
# 配置logrotate
sudo cat > /etc/logrotate.d/anime-website << 'EOF'
/var/log/anime-website/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
EOF
```

### 2. 备份策略

```bash
# 数据库备份脚本
cat > backup-db.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker exec anime-mysql mysqldump -u root -p$MYSQL_ROOT_PASSWORD anime_db > backup_$DATE.sql
gzip backup_$DATE.sql
# 保留30天的备份
find . -name "backup_*.sql.gz" -mtime +30 -delete
EOF

chmod +x backup-db.sh

# 添加到crontab (每天凌晨2点备份)
echo "0 2 * * * /path/to/backup-db.sh" | crontab -
```

### 3. 性能监控

建议安装以下监控工具：
- **Prometheus + Grafana**: 系统监控
- **PM2**: Node.js进程管理
- **Supervisor**: Python进程管理

## ⚡ 性能优化

### 1. 前端优化

```bash
# 启用Gzip压缩 (Nginx配置)
gzip on;
gzip_comp_level 6;
gzip_types text/plain text/css application/json application/javascript;

# CDN配置 (可选)
# 将静态资源上传到CDN
```

### 2. 数据库优化

```sql
-- 添加常用查询索引
ALTER TABLE animes ADD INDEX idx_status_genre (status, genre);
ALTER TABLE users ADD INDEX idx_email (email);
```

### 3. 缓存策略

- 使用Redis缓存热门数据
- 配置CDN缓存静态资源
- 启用浏览器缓存

## 🚨 故障排除

### 常见问题及解决方案

1. **服务无法启动**: 检查端口占用和权限
2. **数据库连接失败**: 检查连接字符串和网络
3. **前端白屏**: 检查API连接和环境变量
4. **SSL证书错误**: 更新证书或检查配置

### 紧急恢复

```bash
# 快速重启服务
sudo systemctl restart anime-backend anime-frontend nginx

# 回滚数据库
# 从备份恢复: zcat backup_YYYYMMDD_HHMMSS.sql.gz | docker exec -i anime-mysql mysql -u root -p anime_db
```