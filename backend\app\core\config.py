import os
from typing import List, Union
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # 环境配置
    ENVIRONMENT: str = "development"  # development, production
    
    # 数据库配置
    DATABASE_URL: str = "mysql+pymysql://sql23721_hentai:507877550%40lihao@************:3306/sql23721_hentai?charset=utf8mb4"
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 应用配置
    DEBUG: bool = True  # 默认开启，通过model_post_init设置
    CORS_ORIGINS: str = "http://************:3000,http://************:3003,http://localhost:3000,http://localhost:3003,http://127.0.0.1:3000,http://127.0.0.1:3003,https://************:3000,https://localhost:3000"
    
    # 数据库连接池配置
    DB_POOL_SIZE: int = 5
    DB_MAX_OVERFLOW: int = 10
    DB_POOL_RECYCLE: int = 3600  # 连接回收时间（秒）
    DB_POOL_PRE_PING: bool = True  # 连接健康检查
    
    def model_post_init(self, __context) -> None:
        """初始化后处理，根据环境设置DEBUG和连接池参数"""
        self.DEBUG = self.ENVIRONMENT == "development"
        if self.ENVIRONMENT == "production":
            self.DB_POOL_SIZE = 20
            self.DB_MAX_OVERFLOW = 30
        else:
            self.DB_POOL_SIZE = 5
            self.DB_MAX_OVERFLOW = 10
    
    @property
    def cors_origins_list(self) -> List[str]:
        if self.CORS_ORIGINS == "*":
            return ["*"]
        return [origin.strip() for origin in self.CORS_ORIGINS.split(",")]
    
    @property
    def is_production(self) -> bool:
        """判断是否为生产环境"""
        return self.ENVIRONMENT == "production"
    
    class Config:
        env_file = ".env"

settings = Settings()