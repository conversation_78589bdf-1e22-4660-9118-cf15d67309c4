'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Anime, apiClient, Comment as CommentType, Tag, User } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';
import { Play, Heart, Eye, Calendar, TagIcon, ArrowLeft, Share2, Reply, Quote, ThumbsUp, ThumbsDown, Edit2, Trash2, Save, X, ChevronDown, ChevronUp } from 'lucide-react';
import dynamic from 'next/dynamic';
const ArtPlayer = dynamic(() => import('@/components/players/ArtWrapper').then(m => m.default), { ssr: false });
import StillsSection from './StillsSection';

// 使用标准的ScreenOrientation API

export default function AnimeDetailPage() {
  const [anime, setAnime] = useState<Anime | null>(null);
  const [isFavorited, setIsFavorited] = useState(false);
  const [favoriteLoading, setFavoriteLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [relatedAnimes, setRelatedAnimes] = useState<Anime[]>([]);
  const [comments, setComments] = useState<CommentType[]>([]);
  const [commentLoading, setCommentLoading] = useState<boolean>(false);
  const [newComment, setNewComment] = useState<string>('');
  const [attachments, setAttachments] = useState<string[]>([]);
  const [posting, setPosting] = useState<boolean>(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  
  // 回复和引用状态
  const [replyToComment, setReplyToComment] = useState<CommentType | null>(null);
  const [quotedComment, setQuotedComment] = useState<CommentType | null>(null);
  const [editingComment, setEditingComment] = useState<number | null>(null);
  const [editContent, setEditContent] = useState('');
  
  // 收缩展开状态管理
  const [collapsedReplies, setCollapsedReplies] = useState<Set<number>>(new Set());
  
  // 评论分页状态
  const [commentPage, setCommentPage] = useState(1);
  const [totalComments, setTotalComments] = useState(0);
  const [loadingMore, setLoadingMore] = useState(false);
  const commentsPerPage = 20;

  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const params = useParams();
  const animeId = params.id as string;

  useEffect(() => {
    if (animeId) {
      fetchAnime();
      fetchRelatedAnimes();
      fetchComments();
    }
  }, [animeId]);

  useEffect(() => {
    if (anime && isAuthenticated) {
      checkFavoriteStatus();
    }
  }, [anime, isAuthenticated]);

  useEffect(() => {
    const fetchCurrentUser = async () => {
      if (isAuthenticated) {
        try {
          const user = await apiClient.getCurrentUser();
          setCurrentUser(user);
        } catch (error) {
          console.error('Failed to fetch current user:', error);
          setCurrentUser(null);
        }
      } else {
        setCurrentUser(null);
      }
    };

    fetchCurrentUser();
  }, [isAuthenticated]);

  const fetchAnime = async () => {
    try {
      setLoading(true);
      const detail = await apiClient.getAnime(parseInt(animeId));
      setAnime(detail);
      // 增加观看次数 - 分离处理，避免影响主要数据加载
      try {
        await apiClient.incrementViewCount(detail.id);
      } catch (viewError) {
        console.warn('Failed to increment view count:', viewError);
        // 观看次数更新失败不影响主要功能
      }
    } catch (error) {
      console.error('Failed to fetch anime:', error);
      router.push('/');
    } finally {
      setLoading(false);
    }
  };

  const fetchRelatedAnimes = async () => {
    try {
      // 获取智能推荐的相关动漫
      const response = await apiClient.getAnimeRecommendations(parseInt(animeId), 12);
      setRelatedAnimes(response.recommendations);
    } catch (error) {
      console.error('Failed to fetch related animes:', error);
      // 如果推荐API失败，回退到原始逻辑
      try {
        const fallbackResponse = await apiClient.getAnimes({
          skip: 0,
          limit: 12
        });
        setRelatedAnimes(fallbackResponse.animes.filter(a => a.id !== parseInt(animeId)).slice(0, 12));
      } catch (fallbackError) {
        console.error('Fallback also failed:', fallbackError);
      }
    }
  };

  const fetchComments = async (page: number = 1, append: boolean = false) => {
    try {
      if (!append) {
        setCommentLoading(true);
      } else {
        setLoadingMore(true);
      }
      
      const skip = (page - 1) * commentsPerPage;
      // 使用新的带回复的API
      const list = await apiClient.listCommentsWithReplies(parseInt(animeId), {
        skip,
        limit: commentsPerPage
      });
      
      if (append) {
        setComments(prev => [...prev, ...list]);
      } else {
        setComments(list);
      }
      
      // 如果返回的评论数少于每页数量，说明没有更多数据了
      if (list.length < commentsPerPage) {
        setTotalComments(skip + list.length);
      } else {
        // 估算总数（实际应该从后端获取）
        setTotalComments(skip + list.length + 1); // +1 表示可能还有更多
      }
      
      // 设置默认收缩状态（回复数量超过3条的评论默认收缩）
      if (!append) {
        const newCollapsed = new Set<number>();
        const checkAndCollapseComments = (comments: CommentType[]) => {
          comments.forEach(comment => {
            if (comment.replies && comment.replies.length > 3) {
              newCollapsed.add(comment.id);
            }
            if (comment.replies) {
              checkAndCollapseComments(comment.replies);
            }
          });
        };
        checkAndCollapseComments(list);
        setCollapsedReplies(newCollapsed);
      }
      
    } catch (error) {
      console.error('Failed to fetch comments:', error);
    } finally {
      setCommentLoading(false);
      setLoadingMore(false);
    }
  };

  const loadMoreComments = async () => {
    const nextPage = commentPage + 1;
    setCommentPage(nextPage);
    await fetchComments(nextPage, true);
  };

  const addEmoji = (emoji: string) => {
    setNewComment((prev) => prev + emoji);
  };

  const addAttachment = () => {
    const url = prompt('请输入表情包/图片URL');
    if (url && url.trim()) {
      setAttachments((prev) => [...prev, url.trim()]);
    }
  };

  const removeAttachment = (idx: number) => {
    setAttachments((prev) => prev.filter((_, i) => i !== idx));
  };

  // 回复和引用处理函数
  const handleReply = (comment: CommentType) => {
    setReplyToComment(comment);
    setQuotedComment(null); // 回复时清除引用
  };

  const handleQuote = (comment: CommentType) => {
    setQuotedComment(comment);
    setReplyToComment(null); // 引用时清除回复
    // 自动在输入框中添加引用内容
    const quotedText = `> ${comment.user?.username || '匿名用户'}: ${comment.content}\n\n`;
    setNewComment(quotedText);
  };

  const cancelReplyOrQuote = () => {
    setReplyToComment(null);
    setQuotedComment(null);
    setNewComment('');
    setEditingComment(null);
    setEditContent('');
  };

  // 编辑评论处理函数
  const handleEdit = (comment: CommentType) => {
    setEditingComment(comment.id);
    setEditContent(comment.content);
  };

  const cancelEdit = () => {
    setEditingComment(null);
    setEditContent('');
  };

  const submitEdit = async () => {
    if (!editingComment || !editContent.trim()) return;

    try {
      const updatedComment = await apiClient.updateComment(editingComment, editContent.trim());
      
      // 更新本地评论列表
      setComments(prevComments => 
        updateCommentInList(prevComments, updatedComment.id, updatedComment)
      );
      
      setEditingComment(null);
      setEditContent('');
    } catch (error) {
      console.error('Failed to update comment:', error);
    }
  };

  // 点赞/反对处理函数
  const handleLike = async (commentId: number, isLike: boolean) => {
    try {
      const result = await apiClient.toggleCommentLike(commentId, isLike);
      
      // 更新本地评论数据
      setComments(prevComments => 
        updateCommentInList(prevComments, commentId, {
          like_count: result.like_count,
          dislike_count: result.dislike_count,
          user_like_status: result.user_like_status
        })
      );
    } catch (error) {
      console.error('Failed to toggle like:', error);
    }
  };

  // 删除评论处理函数
  const handleDelete = async (commentId: number) => {
    if (!confirm('确定要删除这条评论吗？')) return;

    try {
      await apiClient.deleteComment(commentId);
      
      // 从本地评论列表中移除
      setComments(prevComments => removeCommentFromList(prevComments, commentId));
    } catch (error) {
      console.error('Failed to delete comment:', error);
    }
  };

  // 辅助函数：更新评论列表中的特定评论
  const updateCommentInList = (comments: CommentType[], commentId: number, updates: Partial<CommentType>): CommentType[] => {
    return comments.map(comment => {
      if (comment.id === commentId) {
        return { ...comment, ...updates };
      }
      if (comment.replies) {
        return { ...comment, replies: updateCommentInList(comment.replies, commentId, updates) };
      }
      return comment;
    });
  };

  // 辅助函数：从评论列表中删除特定评论
  const removeCommentFromList = (comments: CommentType[], commentId: number): CommentType[] => {
    return comments.filter(comment => {
      if (comment.id === commentId) {
        return false; // 删除该评论
      }
      if (comment.replies) {
        comment.replies = removeCommentFromList(comment.replies, commentId);
      }
      return true;
    });
  };

  // 切换回复收缩展开状态
  const toggleRepliesCollapse = (commentId: number) => {
    setCollapsedReplies(prev => {
      const newSet = new Set(prev);
      if (newSet.has(commentId)) {
        newSet.delete(commentId);
      } else {
        newSet.add(commentId);
      }
      return newSet;
    });
  };

  const submitComment = async () => {
    if (!anime) return;
    if (!isAuthenticated) {
      router.push('/auth');
      return;
    }
    const content = newComment.trim();
    if (!content) return;
    setPosting(true);
    try {
      const commentData: {
        anime_id: number;
        content: string;
        attachments?: string[];
        parent_id?: number;
        reply_to_user_id?: number;
        quoted_comment_id?: number;
      } = {
        anime_id: anime.id,
        content,
        attachments: attachments.length ? attachments : undefined,
      };

      // 添加回复参数
      if (replyToComment) {
        commentData.parent_id = replyToComment.id;
        commentData.reply_to_user_id = replyToComment.user?.id;
      }

      // 添加引用参数
      if (quotedComment) {
        commentData.quoted_comment_id = quotedComment.id;
      }

      await apiClient.createComment(anime.id, commentData);
      setNewComment('');
      setAttachments([]);
      setReplyToComment(null);
      setQuotedComment(null);
      // 重置分页并重新获取评论
      setCommentPage(1);
      await fetchComments(1, false);
    } catch (error) {
      console.error('Failed to post comment:', error);
    } finally {
      setPosting(false);
    }
  };

  // 递归渲染评论的函数
  const renderComment = (comment: CommentType, depth: number = 0): React.ReactNode => {
    const maxDepth = 2; // 最大嵌套深度
    const indent = depth * 20; // 缩进像素
    
    return (
      <div key={comment.id} style={{ marginLeft: `${indent}px` }} className="space-y-3">
        <div className="border-l-2 border-gray-200 pl-3">
          {/* 引用的评论显示 */}
          {comment.quoted_comment_id && (
            <div className="mb-2 p-2 bg-gray-50 rounded border-l-4 border-blue-300">
              <div className="text-xs text-gray-500 mb-1">
                引用了 @{comment.quoted_comment?.user?.username || '匿名用户'} 的评论:
              </div>
              <div className="text-sm text-gray-600 line-clamp-2">
                {comment.quoted_comment?.content || '原评论已删除'}
              </div>
            </div>
          )}
          
          {/* 评论主体 */}
          <div className="flex gap-2 md:gap-3">
            <div className="flex-shrink-0">
              <div className="w-6 h-6 md:w-8 md:h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white text-xs md:text-sm font-bold">
                {comment.user?.username?.[0]?.toUpperCase() || 'A'}
              </div>
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-semibold text-sm md:text-base">
                  {comment.user?.username || '匿名用户'}
                </span>
                <span className="text-xs text-muted-foreground">
                  {new Date(comment.created_at).toLocaleString('zh-CN', {
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
                {/* 显示编辑标识 */}
                {comment.is_edited && (
                  <span className="text-xs text-gray-500 italic">(已编辑)</span>
                )}
              </div>
              
              {/* 评论内容区域 */}
              {editingComment === comment.id ? (
                <div className="mb-2">
                  <textarea
                    value={editContent}
                    onChange={(e) => setEditContent(e.target.value)}
                    className="w-full p-2 border rounded-md text-sm resize-none"
                    rows={3}
                    placeholder="编辑评论内容..."
                    maxLength={2000}
                  />
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-xs text-gray-500">
                      {editContent.length}/2000
                    </span>
                    <div className="flex gap-2">
                      <button
                        onClick={async () => {
                          if (editContent.trim() && editContent.trim() !== comment.content) {
                            try {
                              const updatedComment = await apiClient.updateComment(comment.id, editContent.trim());
                              // 更新评论列表中的评论
                              setComments(prevComments => 
                                updateCommentInList(prevComments, comment.id, {
                                  ...updatedComment,
                                  user_like_status: comment.user_like_status
                                })
                              );
                              setEditingComment(null);
                              setEditContent('');
                            } catch (error) {
                              console.error('编辑评论失败:', error);
                              alert('编辑失败，请重试');
                            }
                          }
                        }}
                        className="flex items-center gap-1 px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
                        disabled={!editContent.trim() || editContent.trim() === comment.content}
                      >
                        <Save className="w-3 h-3" />
                        保存
                      </button>
                      <button
                        onClick={() => {
                          setEditingComment(null);
                          setEditContent('');
                        }}
                        className="flex items-center gap-1 px-3 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600 transition-colors"
                      >
                        <X className="w-3 h-3" />
                        取消
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-sm md:text-base text-foreground mb-2 break-words">
                  {comment.content}
                </div>
              )}
              
              {/* 附件显示 */}
              {comment.attachments && comment.attachments.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-2">
                  {comment.attachments.map((url, idx) => (
                    <img
                      key={idx}
                      src={url}
                      alt="attachment"
                      className="max-w-20 md:max-w-32 max-h-20 md:max-h-32 rounded object-cover cursor-pointer hover:opacity-80"
                      onClick={() => window.open(url, '_blank')}
                    />
                  ))}
                </div>
              )}
              
              {/* 操作按钮 */}
              <div className="flex items-center gap-3 text-xs">
                {/* 点赞/反对按钮 */}
                {isAuthenticated && (
                  <>
                    <button
                      onClick={() => handleLike(comment.id, true)}
                      className={`flex items-center gap-1 transition-colors ${
                        comment.user_like_status === true 
                          ? 'text-blue-500' 
                          : 'text-gray-500 hover:text-blue-500'
                      }`}
                    >
                      <ThumbsUp className="w-3 h-3" />
                      <span>{comment.like_count || 0}</span>
                    </button>
                    
                    <button
                      onClick={() => handleLike(comment.id, false)}
                      className={`flex items-center gap-1 transition-colors ${
                        comment.user_like_status === false 
                          ? 'text-red-500' 
                          : 'text-gray-500 hover:text-red-500'
                      }`}
                    >
                      <ThumbsDown className="w-3 h-3" />
                      <span>{comment.dislike_count || 0}</span>
                    </button>
                  </>
                )}
                
                <button
                  onClick={() => handleReply(comment)}
                  className="flex items-center gap-1 text-gray-500 hover:text-blue-500 transition-colors"
                  disabled={!isAuthenticated}
                >
                  <Reply className="w-3 h-3" />
                  <span>回复</span>
                </button>
                
                <button
                  onClick={() => handleQuote(comment)}
                  className="flex items-center gap-1 text-gray-500 hover:text-green-500 transition-colors"
                  disabled={!isAuthenticated}
                >
                  <Quote className="w-3 h-3" />
                  <span>引用</span>
                </button>
                
                {/* 编辑和删除按钮（只对自己的评论显示） */}
                {isAuthenticated && currentUser && comment.user?.id === currentUser.id && (
                  <>
                    <button
                      onClick={() => handleEdit(comment)}
                      className="flex items-center gap-1 text-gray-500 hover:text-orange-500 transition-colors"
                    >
                      <Edit2 className="w-3 h-3" />
                      <span>编辑</span>
                    </button>
                    
                    <button
                      onClick={() => handleDelete(comment.id)}
                      className="flex items-center gap-1 text-gray-500 hover:text-red-500 transition-colors"
                    >
                      <Trash2 className="w-3 h-3" />
                      <span>删除</span>
                    </button>
                  </>
                )}
              </div>
              
              {/* 回复收缩/展开控制按钮 */}
              {depth < maxDepth && comment.replies && comment.replies.length > 0 && (
                <div className="mt-2">
                  <button
                    onClick={() => toggleRepliesCollapse(comment.id)}
                    className="flex items-center gap-1 text-xs text-blue-500 hover:text-blue-700 transition-colors"
                  >
                    {collapsedReplies.has(comment.id) ? (
                      <>
                        <ChevronDown className="w-3 h-3" />
                        <span>查看 {comment.replies.length} 条回复</span>
                      </>
                    ) : (
                      <>
                        <ChevronUp className="w-3 h-3" />
                        <span>隐藏 {comment.replies.length} 条回复</span>
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* 回复列表渲染 */}
        {depth < maxDepth && comment.replies && comment.replies.length > 0 && !collapsedReplies.has(comment.id) && (
          <div className="space-y-3">
            {comment.replies.map((reply) => renderComment(reply, depth + 1))}
          </div>
        )}
        
        {/* 如果达到最大深度但还有回复，显示"查看更多回复"链接 */}
        {depth >= maxDepth && comment.replies && comment.replies.length > 0 && (
          <div className="text-xs text-blue-500 cursor-pointer hover:text-blue-700">
            查看 {comment.replies.length} 条回复...
          </div>
        )}
      </div>
    );
  };

  const checkFavoriteStatus = async () => {
    if (!anime) return;
    
    try {
      const result = await apiClient.checkAnimeFavorite(anime.id);
      setIsFavorited(result.is_favorited);
    } catch (error) {
      console.error('Failed to check favorite status:', error);
    }
  };

  const toggleFavorite = async () => {
    if (!isAuthenticated || !anime) {
      router.push('/auth');
      return;
    }
    
    setFavoriteLoading(true);
    try {
      if (isFavorited) {
        await apiClient.removeAnimeFavorite(anime.id);
      } else {
        await apiClient.addAnimeFavorite(anime.id);
      }
      setIsFavorited(!isFavorited);
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    } finally {
      setFavoriteLoading(false);
    }
  };

  const formatTags = (tags?: Tag[]) => {
    if (!tags || !Array.isArray(tags)) return [];
    return tags.map(tag => tag.name).filter(Boolean);
  };

  const handleTagClick = (tagName: string) => {
    // 导航到搜索页面，搜索该标签的动漫
    router.push(`/search?search=${encodeURIComponent(tagName)}`);
  };

  const handleShare = async () => {
    if (navigator.share && anime) {
      try {
        await navigator.share({
          title: anime.title,
          text: anime.description,
          url: window.location.href
        });
      } catch (error) {
        // 分享被取消或失败，复制到剪贴板
        copyToClipboard();
      }
    } else {
      copyToClipboard();
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(window.location.href).then(() => {
      alert('链接已复制到剪贴板');
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <div className="text-lg">加载中...</div>
        </div>
      </div>
    );
  }

  if (!anime) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold mb-2">动漫不存在</h1>
          <p className="text-muted-foreground mb-4">
            您访问的动漫可能已被删除或不存在
          </p>
          <Button onClick={() => router.push('/')}>
            返回首页
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Back Button */}
      <Button
        variant="ghost"
        onClick={() => router.back()}
        className="mb-4"
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        返回
      </Button>

      {/* Main Content - Responsive Layout */}
      <div className="grid grid-cols-1 xl:grid-cols-4 gap-4 xl:gap-8">
        
        {/* Left Column - Video and Info */}
        <div className="xl:col-span-3 space-y-4 xl:space-y-6">

          {/* Video Player (ArtPlayer) */}
          {anime.video_url && (
            <Card>
              <CardContent className="p-2 md:p-4 xl:p-6">
                <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
                  <style jsx>{`
                    /* 移动端全屏时的CSS横屏支持 */
                    @media screen and (max-width: 768px) {
                      .art-video-player[data-fullscreen="true"] {
                        transform-origin: center center !important;
                      }
                      
                      @media (orientation: portrait) and (max-height: 500px) {
                        .art-video-player[data-fullscreen="true"] {
                          transform: rotate(90deg) !important;
                          width: 100vh !important;
                          height: 100vw !important;
                          position: fixed !important;
                          top: 0 !important;
                          left: 0 !important;
                          z-index: 999999 !important;
                        }
                      }
                    }
                  `}</style>
                  <ArtPlayer
                    option={{
                      url: anime.video_url,
                      poster: (Array.isArray(anime.fanart) ? anime.fanart[0] : anime.fanart) || anime.cover,
                      volume: 0.7,
                      autoplay: false,
                      autoSize: true,
                      autoMini: false, // 禁用自动画中画
                      screenshot: true,
                      setting: true,
                      hotkey: true,
                      pip: true,
                      mutex: true,
                      fullscreen: true,
                      fullscreenWeb: true,
                      playbackRate: true,
                      aspectRatio: true,
                      theme: '#7c3aed',
                      muted: false,
                      loop: false,
                      // 设置空的右键菜单，移除默认的统计信息和ArtPlayer版本显示
                      contextmenu: [],
                    }}
                    getInstance={(art) => {
                      // 完全禁用右键菜单
                      if (art && art.contextmenu) {
                        art.contextmenu.show = false;
                      }
                      
                      // 阻止右键菜单显示
                      if (art && art.template && art.template.$container) {
                        art.template.$container.addEventListener('contextmenu', (e: Event) => {
                          e.preventDefault();
                          e.stopPropagation();
                          return false;
                        });
                      }
                      
                      // 监听全屏事件，在移动端默认横屏
                      if (art) {
                        const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
                        
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        (art as any).on('fullscreen', (isFullscreen: boolean) => {
                          if (isFullscreen && isMobile) {
                            // 多种方式尝试横屏显示
                            const handleMobileLandscape = async () => {
                              // 检查当前是否为竖屏模式
                              const isPortrait = window.innerHeight > window.innerWidth;
                              
                              // 方法1: 尝试Screen Orientation API (landscape-primary更兼容)
                              try {
                                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                                const orientation = screen.orientation as any;
                                if (orientation && orientation.lock) {
                                  await orientation.lock('landscape-primary');
                                  console.log('Screen locked to landscape-primary orientation');
                                  return;
                                }
                              } catch (err) {
                                console.log('Screen orientation lock failed, trying alternatives:', err);
                              }
                              
                              // 方法2: 尝试不同的landscape值
                              try {
                                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                                const orientation = screen.orientation as any;
                                if (orientation && orientation.lock) {
                                  await orientation.lock('landscape');
                                  console.log('Screen locked to landscape orientation');
                                  return;
                                }
                              } catch (err) {
                                console.log('Landscape lock failed:', err);
                              }
                              
                              // 方法4: 仅在竖屏时添加CSS样式强制横屏显示
                              if (isPortrait) {
                                const videoContainer = art.template.$container;
                                if (videoContainer) {
                                  // 添加数据属性用于CSS选择器
                                  videoContainer.setAttribute('data-forced-landscape', 'true');
                                  
                                  // 直接应用样式
                                  videoContainer.style.transform = 'rotate(90deg)';
                                  videoContainer.style.width = '100vh';
                                  videoContainer.style.height = '100vw';
                                  videoContainer.style.position = 'fixed';
                                  videoContainer.style.top = '0';
                                  videoContainer.style.left = '0';
                                  videoContainer.style.transformOrigin = 'center center';
                                  videoContainer.style.zIndex = '999999';
                                  console.log('Applied CSS landscape transformation for portrait mode');
                                }
                              }
                            };
                            
                            // 延迟执行，确保全屏动画完成
                            setTimeout(handleMobileLandscape, 200);
                          }
                        });
                        
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        (art as any).on('fullscreenExit', () => {
                          // 退出全屏时清理所有方向设置
                          try {
                            // 解除屏幕方向锁定
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any
                            const orientation = screen.orientation as any;
                            if (orientation && orientation.unlock) {
                              orientation.unlock();
                            }
                            
                            // 清理CSS变换
                            const videoContainer = art.template.$container;
                            if (videoContainer) {
                              videoContainer.removeAttribute('data-forced-landscape');
                              videoContainer.style.transform = '';
                              videoContainer.style.width = '';
                              videoContainer.style.height = '';
                              videoContainer.style.position = '';
                              videoContainer.style.top = '';
                              videoContainer.style.left = '';
                              videoContainer.style.transformOrigin = '';
                              videoContainer.style.zIndex = '';
                            }
                            
                            console.log('Screen orientation and CSS transforms cleared');
                          } catch (err) {
                            console.log('Cleanup failed:', err);
                          }
                        });
                      }
                    }}
                    style={{ width: '100%', height: '100%' }}
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Video Info - Moved below video */}
          <div className="space-y-3 md:space-y-4">
            {/* Title and Basic Info */}
            <div className="space-y-2 px-2 md:px-0">
              <h1 className="text-2xl md:text-3xl font-bold">{anime.title}</h1>
              {anime.title_english && (
                <p className="text-base md:text-lg text-muted-foreground">
                  {anime.title_english}
                </p>
              )}
              {anime.title_japanese && (
                <p className="text-sm md:text-base text-muted-foreground">
                  {anime.title_japanese}
                </p>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 px-2 md:px-0">
              <Button
                variant={isFavorited ? "destructive" : "outline"}
                onClick={toggleFavorite}
                disabled={favoriteLoading}
                size="sm"
              >
                <Heart 
                  className={`w-4 h-4 mr-1 md:mr-2 ${isFavorited ? 'fill-current' : ''}`} 
                />
                <span className="hidden md:inline">{isFavorited ? '已收藏' : '收藏'}</span>
                <span className="md:hidden">{isFavorited ? '已收藏' : '收藏'}</span>
              </Button>
              
              <Button variant="outline" size="sm" onClick={handleShare}>
                <Share2 className="w-4 h-4" />
                <span className="hidden md:inline ml-2">分享</span>
              </Button>
            </div>

            {/* Stats */}
            <div className="flex flex-wrap items-center gap-3 md:gap-6 text-xs md:text-sm text-muted-foreground px-2 md:px-0">
              <div className="flex items-center">
                <Eye className="w-4 h-4 mr-1" />
                <span>{anime.view_count} 观看</span>
              </div>
              
              <div className="flex items-center">
                <Heart className="w-4 h-4 mr-1" />
                <span>{anime.favorite_count} 收藏</span>
              </div>

              {anime.release_year && (
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  <span>{anime.release_year}</span>
                </div>
              )}
            </div>
          </div>

          {/* Stills (剧照) 移动到视频下方 */}
          <StillsSection fanart={anime.fanart} />

          {/* Description */}
          {anime.description && (
            <Card>
              <CardContent className="p-4 md:p-6">
                <h3 className="text-lg font-semibold mb-3">剧情简介</h3>
                <p className="text-muted-foreground leading-relaxed">
                  {anime.description}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Tags Section - Mobile Only (显示在剧情简介下方) */}
          <div className="xl:hidden">
            <Card>
              <CardContent className="p-4">
                <h3 className="text-lg font-semibold mb-4">标签</h3>
                <div className="flex flex-wrap gap-2">
                  {anime.category && (
                    <Badge variant="outline" className="text-sm">
                      {anime.category}
                    </Badge>
                  )}
                  
                  {formatTags(anime.tags).map((tag, index) => (
                    <Badge 
                      key={index} 
                      variant="secondary" 
                      className="text-sm cursor-pointer hover:bg-secondary/80 transition-colors"
                      onClick={() => handleTagClick(tag)}
                    >
                      <TagIcon className="w-3 h-3 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Related Animes */}
          {relatedAnimes.length > 0 && (
            <Card>
              <CardContent className="p-4 md:p-6">
                <h3 className="text-lg font-semibold mb-4">相关推荐</h3>
                <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3 md:gap-4">{relatedAnimes.map((relatedAnime) => (
                    <div 
                      key={relatedAnime.id}
                      className="cursor-pointer group"
                      onClick={() => router.push(`/anime/${relatedAnime.id}`)}
                    >
                      <div className="relative aspect-[3/4] overflow-hidden rounded-lg">
                        <img
                          src={relatedAnime.cover || 'https://picsum.photos/400/600?blur=3'}
                          alt={relatedAnime.title}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = 'https://picsum.photos/400/600?blur=3';
                          }}
                        />
                        
                        {/* Title Overlay - Same as AnimeCard */}
                        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-2">
                          <h4 className="text-white font-bold text-xs md:text-sm line-clamp-1 leading-tight">
                            {relatedAnime.title}
                          </h4>
                        </div>
                        
                        {/* Hover Overlay */}
                        <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                          <Button size="sm" variant="secondary">
                            <Play className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Comments */}
          <Card>
            <CardContent className="p-3 md:p-6 space-y-4">
              <h3 className="text-lg font-semibold">评论</h3>

              {/* New comment form */}
              <div className="space-y-2">
                {/* 回复/引用上下文显示 */}
                {(replyToComment || quotedComment) && (
                  <div className="p-3 bg-gray-50 border rounded-md">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700">
                        {replyToComment ? '正在回复:' : '正在引用:'}
                      </span>
                      <button
                        onClick={cancelReplyOrQuote}
                        className="text-gray-500 hover:text-gray-700 text-sm"
                      >
                        取消
                      </button>
                    </div>
                    <div className="text-sm text-gray-600">
                      <strong>{(replyToComment || quotedComment)?.user?.username || '匿名用户'}：</strong>
                      <span className="line-clamp-2">
                        {(replyToComment || quotedComment)?.content}
                      </span>
                    </div>
                  </div>
                )}
                
                <textarea
                  className="w-full min-h-[80px] md:min-h-[90px] p-2 md:p-3 rounded-md border bg-background text-sm md:text-base"
                  placeholder={replyToComment ? "写下你的回复..." : quotedComment ? "引用并发表你的看法..." : "说点什么..."}
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                />
                <div className="flex flex-wrap items-center gap-2">
                  {/* Quick emoji bar */}
                  <div className="flex flex-wrap gap-1">
                    {['😀','😍','😎','😡','😭','👍','👎','🔥','💯'].map((emo) => (
                      <button
                        key={emo}
                        type="button"
                        className="px-1 md:px-2 py-1 text-lg md:text-xl hover:opacity-80"
                        onClick={() => addEmoji(emo)}
                        title="插入表情"
                      >
                        {emo}
                      </button>
                    ))}
                  </div>
                  <div className="flex flex-wrap gap-2 mt-2 md:mt-0">
                    <Button type="button" variant="outline" size="sm" onClick={addAttachment}>
                      <span className="hidden md:inline">添加表情包</span>
                      <span className="md:hidden">表情包</span>
                    </Button>
                    <Button type="button" size="sm" onClick={submitComment} disabled={posting}>
                      <span className="hidden md:inline">发表评论</span>
                      <span className="md:hidden">发表</span>
                    </Button>
                  </div>
                </div>
                {/* Attachments preview */}
                {attachments.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {attachments.map((url, idx) => (
                      <div key={`${url}-${idx}`} className="relative">
                        <img src={url} alt="attachment" className="w-16 h-16 object-cover rounded" />
                        <button
                          className="absolute -top-2 -right-2 bg-black/70 text-white rounded-full w-6 h-6"
                          onClick={() => removeAttachment(idx)}
                          title="移除"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Comment list */}
              <div className="space-y-4">
                {commentLoading ? (
                  <div className="text-sm text-muted-foreground">评论加载中...</div>
                ) : comments.length === 0 ? (
                  <div className="text-sm text-muted-foreground">还没有评论，来抢沙发吧～</div>
                ) : (
                  <>
                    {comments.map((comment) => renderComment(comment, 0))}
                    
                    {/* 加载更多按钮 */}
                    {comments.length >= commentsPerPage && comments.length < totalComments && (
                      <div className="flex justify-center pt-4">
                        <Button 
                          variant="outline" 
                          onClick={loadMoreComments}
                          disabled={loadingMore}
                          className="w-full md:w-auto"
                        >
                          {loadingMore ? '加载中...' : `加载更多评论 (已显示 ${comments.length}/${totalComments > comments.length ? '更多' : totalComments})`}
                        </Button>
                      </div>
                    )}
                    
                    {/* 显示评论统计 */}
                    <div className="text-xs text-muted-foreground text-center pt-2">
                      {totalComments > 0 && (
                        comments.length >= totalComments 
                          ? `共 ${totalComments} 条评论，已全部加载`
                          : `已显示 ${comments.length} 条评论，按最新发布时间排序`
                      )}
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Tags (Desktop Only) */}
        <div className="xl:col-span-1 hidden xl:block">
          <div className="sticky top-8 space-y-6">
            {/* Category and Tags */}
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4">标签</h3>
                <div className="flex flex-wrap gap-2">
                  {anime.category && (
                    <Badge variant="outline" className="text-sm">
                      {anime.category}
                    </Badge>
                  )}
                  
                  {formatTags(anime.tags).map((tag, index) => (
                    <Badge 
                      key={index} 
                      variant="secondary" 
                      className="text-sm cursor-pointer hover:bg-secondary/80 transition-colors"
                      onClick={() => handleTagClick(tag)}
                    >
                      <TagIcon className="w-3 h-3 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}