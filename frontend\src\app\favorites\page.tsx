'use client';

import { useState, useEffect } from 'react';
import { AnimeCard } from '@/components/anime/AnimeCard';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Anime, Favorite, apiClient } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';
import { Heart, Search, Filter, Trash2 } from 'lucide-react';

export default function FavoritesPage() {
  const [favorites, setFavorites] = useState<Favorite[]>([]);
  const [filteredFavorites, setFilteredFavorites] = useState<Favorite[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  
  const { isAuthenticated, user } = useAuth();
  const itemsPerPage = 12;

  // Get unique categories from favorites
  const categories = Array.from(
    new Set(favorites.map(fav => fav.anime?.category || fav.manga?.category).filter(Boolean))
  );

  useEffect(() => {
    if (isAuthenticated) {
      fetchFavorites();
    }
  }, [isAuthenticated]);

  useEffect(() => {
    filterFavorites();
  }, [favorites, searchTerm, selectedCategory, currentPage]);

  const fetchFavorites = async () => {
    try {
      setLoading(true);
      const response = await apiClient.getFavorites();
      setFavorites(response);
    } catch (error) {
      console.error('Failed to fetch favorites:', error);
      setFavorites([]);
    } finally {
      setLoading(false);
    }
  };

  const filterFavorites = () => {
    let filtered = favorites;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(anime =>
        anime.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (anime.title_english && anime.title_english.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (anime.description && anime.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply category filter
    if (selectedCategory) {
      filtered = filtered.filter(anime => anime.category === selectedCategory);
    }

    // Calculate pagination
    const total = filtered.length;
    setTotalPages(Math.ceil(total / itemsPerPage));

    // Apply pagination
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    setFilteredFavorites(filtered.slice(startIndex, endIndex));
  };

  const handleRemoveFromFavorites = async (animeId: number) => {
    try {
      await apiClient.removeFavorite(animeId);
      setFavorites(prev => prev.filter(anime => anime.id !== animeId));
    } catch (error) {
      console.error('Failed to remove from favorites:', error);
    }
  };

  const handleClearAllFavorites = async () => {
    if (confirm('确定要清空所有收藏吗？此操作不可撤销。')) {
      try {
        // Remove all favorites one by one
        await Promise.all(
          favorites.map(anime => apiClient.removeFavorite(anime.id))
        );
        setFavorites([]);
      } catch (error) {
        console.error('Failed to clear favorites:', error);
      }
    }
  };

  const handleSearch = () => {
    setCurrentPage(1);
    filterFavorites();
  };

  const handleCategoryFilter = (category: string) => {
    setSelectedCategory(category === selectedCategory ? '' : category);
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCategory('');
    setCurrentPage(1);
  };

  const handleFavoriteChange = () => {
    // Refresh favorites when a favorite is toggled
    fetchFavorites();
  };

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <Heart className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h1 className="text-2xl font-bold mb-2">收藏功能</h1>
          <p className="text-muted-foreground mb-4">
            请先登录以查看你的收藏列表
          </p>
          <Button asChild>
            <Link href="/auth">立即登录</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">我的收藏</h1>
          <p className="text-muted-foreground">
            {user?.username}，你总共收藏了 {favorites.length} 部动漫
          </p>
        </div>
        {favorites.length > 0 && (
          <Button 
            variant="outline" 
            onClick={handleClearAllFavorites}
            className="text-destructive hover:text-destructive"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            清空收藏
          </Button>
        )}
      </div>

      {loading ? (
        <div className="text-center py-12">
          <div className="text-lg">加载中...</div>
        </div>
      ) : favorites.length === 0 ? (
        <div className="text-center py-12">
          <Heart className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-xl font-semibold mb-2">暂无收藏</h2>
          <p className="text-muted-foreground mb-4">
            你还没有收藏任何动漫，快去发现喜欢的作品吧！
          </p>
          <Button asChild>
            <Link href="/">浏览动漫</Link>
          </Button>
        </div>
      ) : (
        <>
          {/* Search and Filters */}
          <div className="bg-card border rounded-lg p-4 space-y-4">
            {/* Search Input */}
            <div className="flex gap-2">
              <Input
                placeholder="搜索你的收藏..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="flex-1"
              />
              <Button onClick={handleSearch}>
                <Search className="w-4 h-4" />
              </Button>
            </div>

            {/* Category Filters */}
            {categories.length > 0 && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Filter className="w-4 h-4" />
                  <span className="text-sm font-medium">按分类筛选:</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <Badge
                      key={category}
                      variant={selectedCategory === category ? "default" : "outline"}
                      className="cursor-pointer hover:bg-primary/10"
                      onClick={() => handleCategoryFilter(category || '')}
                    >
                      {category}
                    </Badge>
                  ))}
                  {(searchTerm || selectedCategory) && (
                    <Button variant="ghost" size="sm" onClick={clearFilters}>
                      清除筛选
                    </Button>
                  )}
                </div>
              </div>
            )}

            {/* Active Filters */}
            {(searchTerm || selectedCategory) && (
              <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm text-muted-foreground">当前筛选:</span>
                {searchTerm && (
                  <Badge variant="secondary">
                    搜索: &quot;{searchTerm}&quot;
                  </Badge>
                )}
                {selectedCategory && (
                  <Badge variant="secondary">
                    分类: {selectedCategory}
                  </Badge>
                )}
              </div>
            )}
          </div>

          {/* Results */}
          <>
            {/* Results Header */}
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">
                {searchTerm || selectedCategory 
                  ? `筛选结果 (${filteredFavorites.length} 个结果)` 
                  : '收藏列表'
                }
              </h2>
              {totalPages > 1 && (
                <span className="text-sm text-muted-foreground">
                  第 {currentPage} 页，共 {totalPages} 页
                </span>
              )}
            </div>

            {/* Favorites Grid */}
            {filteredFavorites.length > 0 ? (
              <>
                <div className="grid grid-cols-3 md:grid-cols-7 gap-4">
                  {filteredFavorites.map((anime) => (
                    <div key={anime.id} className="relative group">
                      <AnimeCard 
                        anime={anime} 
                        onFavoriteChange={handleFavoriteChange}
                      />
                      {/* Remove from favorites button */}
                      <Button
                        size="sm"
                        variant="destructive"
                        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => handleRemoveFromFavorites(anime.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center gap-2 mt-8">
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                    >
                      上一页
                    </Button>
                    <span className="flex items-center px-4">
                      {currentPage} / {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                    >
                      下一页
                    </Button>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <p className="text-lg text-muted-foreground">
                  没有找到匹配的收藏，请尝试其他筛选条件
                </p>
              </div>
            )}
          </>
        </>
      )}
    </div>
  );
}