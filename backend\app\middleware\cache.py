"""
缓存中间件 - 提升API响应速度
"""
import json
import hashlib
from typing import Optional, Any
from functools import wraps
import time
import logging

logger = logging.getLogger(__name__)

class SimpleMemoryCache:
    """简单的内存缓存实现"""
    
    def __init__(self, default_ttl: int = 300):
        self._cache = {}
        self._timestamps = {}
        self.default_ttl = default_ttl
    
    def _is_expired(self, key: str) -> bool:
        """检查缓存项是否过期"""
        if key not in self._timestamps:
            return True
        return time.time() - self._timestamps[key] > self.default_ttl
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        if key in self._cache and not self._is_expired(key):
            return self._cache[key]
        
        # 清理过期项
        if key in self._cache:
            del self._cache[key]
            del self._timestamps[key]
        
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存项"""
        self._cache[key] = value
        self._timestamps[key] = time.time()
    
    def delete(self, key: str) -> None:
        """删除缓存项"""
        if key in self._cache:
            del self._cache[key]
            del self._timestamps[key]
    
    def clear(self) -> None:
        """清空所有缓存"""
        self._cache.clear()
        self._timestamps.clear()
    
    def cleanup_expired(self) -> None:
        """清理所有过期缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self._timestamps.items()
            if current_time - timestamp > self.default_ttl
        ]
        
        for key in expired_keys:
            del self._cache[key]
            del self._timestamps[key]

# 全局缓存实例
cache = SimpleMemoryCache(default_ttl=300)  # 5分钟默认过期时间

def cache_key_generator(*args, **kwargs) -> str:
    """生成缓存键"""
    key_data = {
        'args': args,
        'kwargs': {k: v for k, v in kwargs.items() if k != 'db'}  # 排除db对象
    }
    key_str = json.dumps(key_data, sort_keys=True, default=str)
    return hashlib.md5(key_str.encode()).hexdigest()

def cached_api(ttl: int = 300, key_prefix: str = ""):
    """API缓存装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{func.__name__}:{cache_key_generator(*args, **kwargs)}"
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                logger.info(f"Cache hit for {cache_key}")
                return cached_result
            
            # 执行原函数
            result = await func(*args, **kwargs)
            
            # 缓存结果
            cache.set(cache_key, result, ttl)
            logger.info(f"Cache set for {cache_key}")
            
            return result
        
        return wrapper
    return decorator

def invalidate_cache_pattern(pattern: str):
    """根据模式清除缓存"""
    keys_to_delete = [key for key in cache._cache.keys() if pattern in key]
    for key in keys_to_delete:
        cache.delete(key)
    logger.info(f"Invalidated {len(keys_to_delete)} cache entries matching pattern: {pattern}")

class CacheStats:
    """缓存统计"""
    
    @staticmethod
    def get_stats() -> dict:
        """获取缓存统计信息"""
        cache.cleanup_expired()  # 先清理过期项
        
        return {
            "total_items": len(cache._cache),
            "memory_usage_estimate": sum(
                len(str(key)) + len(str(value)) 
                for key, value in cache._cache.items()
            ),
            "oldest_item_age": (
                time.time() - min(cache._timestamps.values()) 
                if cache._timestamps else 0
            ),
        }
    
    @staticmethod
    def clear_all():
        """清空所有缓存"""
        cache.clear()
        logger.info("All cache cleared")

# 预定义的缓存策略
CACHE_STRATEGIES = {
    "anime_list": {"ttl": 180, "key_prefix": "anime_list"},      # 3分钟
    "anime_detail": {"ttl": 600, "key_prefix": "anime_detail"},  # 10分钟
    "featured": {"ttl": 900, "key_prefix": "featured"},          # 15分钟
    "recommendations": {"ttl": 1800, "key_prefix": "recommendations"},  # 30分钟
    "search": {"ttl": 300, "key_prefix": "search"},              # 5分钟
}

def get_cache_decorator(strategy: str):
    """根据策略获取缓存装饰器"""
    if strategy not in CACHE_STRATEGIES:
        raise ValueError(f"Unknown cache strategy: {strategy}")
    
    config = CACHE_STRATEGIES[strategy]
    return cached_api(ttl=config["ttl"], key_prefix=config["key_prefix"])