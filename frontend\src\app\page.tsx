'use client';

import { useState, useEffect } from 'react';
import { AnimeCard } from '@/components/anime/AnimeCard';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Anime, FeaturedAnime, apiClient } from '@/lib/api';
import { Search, Play, ChevronLeft, ChevronRight } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function Home() {
  const [animes, setAnimes] = useState<Anime[]>([]);
  const [featuredAnimes, setFeaturedAnimes] = useState<Anime[]>([]);
  const [heroAnimes, setHeroAnimes] = useState<Anime[]>([]);
  const [currentHeroIndex, setCurrentHeroIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  
  // 触摸滑动状态
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const [posterScrollLeft, setPosterScrollLeft] = useState(0);
  const [isSwipeGesture, setIsSwipeGesture] = useState(false); // 标记是否为滑动手势

  const router = useRouter();

  // 轮播滑动手势处理
  const handleTouchStart = (e: React.TouchEvent) => {
    e.stopPropagation(); // 防止事件冒泡
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
    setIsSwipeGesture(false); // 重置滑动标记
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    e.stopPropagation(); // 防止事件冒泡
    const currentTouch = e.targetTouches[0].clientX;
    setTouchEnd(currentTouch);
    
    // 如果移动距离超过10px，标记为滑动手势
    if (touchStart && Math.abs(touchStart - currentTouch) > 10) {
      setIsSwipeGesture(true);
      e.preventDefault(); // 防止默认滚动行为
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    e.stopPropagation(); // 防止事件冒泡
    
    if (!touchStart || !touchEnd) {
      setTouchStart(null);
      setTouchEnd(null);
      return;
    }
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if ((isLeftSwipe || isRightSwipe) && heroAnimes.length > 1) {
      setIsSwipeGesture(true);
      if (isLeftSwipe) {
        nextHero();
      } else if (isRightSwipe) {
        prevHero();
      }
    }
    
    // 延迟重置状态，确保点击事件能正确判断
    setTimeout(() => {
      setTouchStart(null);
      setTouchEnd(null);
      setIsSwipeGesture(false);
    }, 100);
  };

  // 海报栏滑动处理
  const handlePosterTouchStart = (e: React.TouchEvent) => {
    const scrollContainer = e.currentTarget as HTMLDivElement;
    setPosterScrollLeft(scrollContainer.scrollLeft);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handlePosterTouchMove = (e: React.TouchEvent) => {
    if (!touchStart) return;
    
    const currentTouch = e.targetTouches[0].clientX;
    const diff = touchStart - currentTouch;
    const scrollContainer = e.currentTarget as HTMLDivElement;
    
    scrollContainer.scrollLeft = posterScrollLeft + diff;
  };

  useEffect(() => {
    // 优化后的数据加载策略
    const initializePageData = async () => {
      try {
        setLoading(true);
        
        // 优先加载主要内容（动漫列表）
        await fetchAnimes();
        
        // 并行加载次要内容（不影响主要loading状态）
        Promise.allSettled([
          fetchFeaturedAnimes(),
          fetchHeroAnimes()
        ]).then(results => {
          results.forEach((result, index) => {
            if (result.status === 'rejected') {
              console.warn(`Secondary data load ${index} failed:`, result.reason);
            }
          });
        });
        
      } catch (error) {
        console.error('Failed to initialize page data:', error);
      } finally {
        setLoading(false);
      }
    };
    
    initializePageData();
  }, [currentPage]);

  // 自动轮播效果
  useEffect(() => {
    if (heroAnimes.length > 1) {
      const interval = setInterval(() => {
        setCurrentHeroIndex((prevIndex) => 
          (prevIndex + 1) % heroAnimes.length
        );
      }, 20000); // 每20秒切换一次

      return () => clearInterval(interval);
    }
  }, [heroAnimes.length]);

  // 手动切换函数
  const nextHero = () => {
    setCurrentHeroIndex((prevIndex) => 
      (prevIndex + 1) % heroAnimes.length
    );
  };

  const prevHero = () => {
    setCurrentHeroIndex((prevIndex) => 
      prevIndex === 0 ? heroAnimes.length - 1 : prevIndex - 1
    );
  };

  const goToHero = (index: number) => {
    setCurrentHeroIndex(index);
  };

  const fetchAnimes = async () => {
    try {
      const skip = (currentPage - 1) * 42;
      const response = await apiClient.getAnimes({
        skip: skip,
        limit: 42,
        search: searchTerm || undefined
      });
      
      setAnimes(response.animes || []);
      setTotalPages(Math.ceil((response.total || 0) / 42));
      return response; // 返回结果供 Promise.allSettled使用
    } catch (error) {
      console.error('Failed to fetch animes:', error);
      setAnimes([]);
      setTotalPages(1);
      throw error; // 重新抛出错误
    }
  };

  const fetchFeaturedAnimes = async () => {
    try {
      // 由于移除了 is_featured 字段，海报栏显示最新的6个动漫
      const response = await apiClient.getAnimes({
        skip: 0,
        limit: 6
      });
      
      setFeaturedAnimes(response.animes || []);
      return response;
    } catch (error) {
      console.error('Failed to fetch featured animes:', error);
      setFeaturedAnimes([]);
      throw error;
    }
  };

  const fetchHeroAnimes = async () => {
    try {
      // 使用推荐API获取首页轮播动漫
      const response = await apiClient.getFeaturedAnimes(6);
      
      setHeroAnimes(response.animes || []);
      return response;
    } catch (error) {
      console.error('Failed to fetch hero animes:', error);
      setHeroAnimes([]);
      throw error;
    }
  };

  const handleSearch = () => {
    setCurrentPage(1);
    fetchAnimes();
  };

  // 分类筛选已移除

  const handleFavoriteChange = () => {
    // Refresh the anime list to update favorite counts
    fetchAnimes();
  };

  return (
    <div className="container mx-auto px-4 py-8 space-y-8 max-w-full overflow-x-hidden">
      {/* Hero Section - 大图轮播 */}
      {heroAnimes && heroAnimes.length > 0 && (
        <section className="relative">
          <div 
            className="relative aspect-video md:aspect-[2/1] rounded-lg overflow-hidden group"
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            style={{
              userSelect: 'none',
              WebkitUserSelect: 'none',
              touchAction: 'pan-y', // 只允许垂直滚动，水平滑动用于轮播切换
              WebkitTouchCallout: 'none' // 禁用长按菜单
            }}
          >
            {/* 当前显示的剧照 */}
            {heroAnimes.map((anime, index) => {
              // 图片优先级：custom_poster_url > fanart > cover
              let stillImage = anime.custom_poster_url;
              const isCustomPoster = !!anime.custom_poster_url;
              
              if (!stillImage) {
                if (Array.isArray(anime.fanart) && anime.fanart.length > 0) {
                  stillImage = anime.fanart[Math.floor(Math.random() * anime.fanart.length)];
                } else {
                  stillImage = anime.cover;
                }
              }
              
              return (
                <div
                  key={anime.id}
                  className={`absolute inset-0 transition-opacity duration-1000 ${
                    index === currentHeroIndex ? 'opacity-100' : 'opacity-0'
                  }`}
                >
                  {isCustomPoster ? (
                    // 自定义海报：双层显示，确保完整显示用户选择的图片
                    <>
                      {/* 背景层：模糊填充 */}
                      <div
                        className="absolute inset-0 bg-cover bg-center filter blur-sm scale-110"
                        style={{
                          backgroundImage: `url(${stillImage || ''})`
                        }}
                      />
                      
                      {/* 前景层：清晰图片，完整显示 */}
                      <img
                        src={stillImage || 'https://picsum.photos/1200/600?blur=3'}
                        alt={anime.title}
                        className="relative z-10 w-full h-full object-scale-down"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = 'https://picsum.photos/1200/600?blur=3';
                        }}
                      />
                    </>
                  ) : (
                    // 剧照/封面：直接填充显示，优先视觉效果
                    <img
                      src={stillImage || ''}
                      alt={anime.title}
                      className="w-full h-full object-cover"
                    />
                  )}
                  
                  {/* 遮罩层 */}
                  <div className="absolute inset-0 bg-black/30 z-20" />
                  
                  {/* 内容层 */}
                  <div 
                    className="absolute inset-0 flex items-center justify-center cursor-pointer z-30"
                    onClick={(e) => {
                      // 只在没有滑动手势时才导航
                      if (!isSwipeGesture) {
                        e.stopPropagation();
                        router.push(`/anime/${anime.id}`);
                      }
                    }}
                  >
                    {/* 播放按钮 */}
                    <div className="bg-white/20 backdrop-blur-sm rounded-full p-4 md:p-6 group-hover:bg-white/30 transition-colors duration-300">
                      <Play className="w-8 h-8 md:w-12 md:h-12 text-white fill-current" />
                    </div>
                  </div>
                  
                  {/* 右下角封面和信息 */}
                  <div className="absolute bottom-4 right-4 md:bottom-6 md:right-6 flex items-end gap-3 z-30">
                    <div className="text-right max-w-xs">
                      <h3 className="text-white font-bold text-lg md:text-xl mb-1">
                        {anime.title}
                      </h3>
                      {anime.description && (
                        <p className="text-white/80 text-sm md:text-base line-clamp-2">
                          {anime.description}
                        </p>
                      )}
                    </div>
                    <img
                      src={anime.cover || 'https://picsum.photos/120/180?blur=3'}
                      alt={`${anime.title} 封面`}
                      className="w-16 h-24 md:w-20 md:h-30 object-cover rounded border-2 border-white/80 shadow-lg"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = 'https://picsum.photos/120/180?blur=3';
                      }}
                    />
                  </div>
                </div>
              );
            })}
            
            {/* 左右切换按钮 */}
            {heroAnimes.length > 1 && (
              <>
                <button
                  onClick={prevHero}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-colors duration-300 opacity-0 group-hover:opacity-100 z-40"
                >
                  <ChevronLeft className="w-6 h-6" />
                </button>
                <button
                  onClick={nextHero}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-colors duration-300 opacity-0 group-hover:opacity-100 z-40"
                >
                  <ChevronRight className="w-6 h-6" />
                </button>
              </>
            )}
          </div>
          
          {/* 底部指示器 */}
          {heroAnimes.length > 1 && (
            <div className="flex justify-center mt-4 space-x-2">
              {heroAnimes.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToHero(index)}
                  className={`w-3 h-3 rounded-full transition-colors duration-300 ${
                    index === currentHeroIndex 
                      ? 'bg-primary' 
                      : 'bg-gray-300 hover:bg-gray-400'
                  }`}
                />
              ))}
            </div>
          )}
        </section>
      )}

      {/* 海报栏（横向滚动） */}
      {featuredAnimes && featuredAnimes.length > 0 && (
        <section className="overflow-x-hidden">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-2xl font-semibold">海报栏</h2>
          </div>
          <div 
            className="overflow-x-auto scrollbar-hide touch-pan-x smooth-scroll"
            onTouchStart={handlePosterTouchStart}
            onTouchMove={handlePosterTouchMove}
            style={{
              WebkitOverflowScrolling: 'touch'
            }}
          >
            <div className="flex gap-4 min-w-full pb-2">
              {featuredAnimes.map((anime) => (
                <div key={anime.id} className="w-48 shrink-0">
                  <AnimeCard anime={anime} onFavoriteChange={handleFavoriteChange} />
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* 分类筛选已移除 */}

      {/* Anime Grid */}
      <section className="overflow-x-hidden">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-semibold">全部视频</h2>
          <span className="text-sm text-muted-foreground">
            第 {currentPage} 页，共 {totalPages} 页
          </span>
        </div>

        {loading ? (
          <div className="text-center py-12">
            <div className="text-lg">加载中...</div>
          </div>
        ) : animes && animes.length > 0 ? (
          <>
            <div className="grid grid-cols-3 md:grid-cols-7 gap-4 w-full max-w-full overflow-x-hidden">
              {animes.map((anime) => (
                <AnimeCard 
                  key={anime.id} 
                  anime={anime} 
                  onFavoriteChange={handleFavoriteChange}
                />
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center gap-2 mt-8 flex-wrap">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  上一页
                </Button>
                <span className="flex items-center px-4">
                  {currentPage} / {totalPages}
                </span>
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  下一页
                </Button>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <p className="text-lg text-muted-foreground">
              {searchTerm ? '没有找到匹配的视频' : '暂无内容'}
            </p>
          </div>
        )}
      </section>
    </div>
  );
}
