'use client';

import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Anime } from '@/lib/api';
import { Play } from 'lucide-react';
import { useOptimizedImage } from '@/hooks/useOptimizedImage';

interface AnimeCardProps {
  anime: Anime;
  onFavoriteChange?: () => void; // 保留接口兼容性，但不使用
}

export const AnimeCard: React.FC<AnimeCardProps> = ({ anime }) => {
  const { src: optimizedSrc, isLoading } = useOptimizedImage({
    src: anime.cover,
    title: anime.title,
    type: 'cover'
  });

  return (
    <Card className="group hover:shadow-lg transition-shadow duration-200 w-full max-w-full">
      <CardContent className="p-0 w-full max-w-full overflow-hidden">
        {/* Cover Image with Title Overlay */}
        <div className="relative aspect-[3/4] overflow-hidden rounded-lg w-full">
          {isLoading ? (
            <div className="w-full h-full bg-gradient-to-br from-gray-300 to-gray-400 animate-pulse" />
          ) : (
            <img
              src={optimizedSrc}
              alt={anime.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
            />
          )}
          
          {/* Title Overlay - Always visible at bottom */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-3">
            <h3 className="text-white font-bold text-sm line-clamp-1 leading-tight">
              {anime.title}
            </h3>
          </div>
          
          {/* Hover Overlay with play button only */}
          <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
            <Link href={`/anime/${anime.id}`}>
              <Button size="sm" variant="secondary">
                <Play className="w-6 h-6" />
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};