from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, ForeignKey, Date, UniqueConstraint, BigInteger, Numeric, Enum
from sqlalchemy.orm import relationship, declarative_base
from sqlalchemy.sql import func
from sqlalchemy.sql import func
import enum

Base = declarative_base()

# 枚举定义
class MangaType(enum.Enum):
    SERIAL = "serial"  # 连载
    TANKOUBON = "tankoubon"  # 单行本
    DOUJINSHI = "doujinshi"  # 同人志

class MangaStatus(enum.Enum):
    ONGOING = "ongoing"  # 连载中
    COMPLETED = "completed"  # 已完结
    HIATUS = "hiatus"  # 休载
    CANCELLED = "cancelled"  # 已取消

class ChapterStatus(enum.Enum):
    DRAFT = "draft"  # 草稿
    PUBLISHED = "published"  # 已发布

class ContentType(enum.Enum):
    ANIME = "anime"  # 动漫
    MANGA = "manga"  # 漫画

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    password_hash = Column(String(100), nullable=False)  # 改为 password_hash
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    avatar_url = Column(String(1000), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    favorites = relationship("Favorite", back_populates="user")
    comments = relationship("Comment", back_populates="user", foreign_keys="[Comment.user_id]")
    manga_reading_progress = relationship("MangaReadingProgress", back_populates="user", cascade="all, delete-orphan")

class Anime(Base):
    __tablename__ = "animes"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(500), nullable=False, index=True)
    title_english = Column(String(500), nullable=True)
    title_japanese = Column(String(500), nullable=True)
    description = Column(Text, nullable=True)
    cover = Column(String(1000), nullable=True)
    fanart = Column(Text, nullable=True)  # 改为Text类型支持大量图片链接
    video_url = Column(String(1000), nullable=False)
    release_year = Column(Integer, nullable=True)
    release_date = Column(Date, nullable=True)
    view_count = Column(Integer, default=0)
    favorite_count = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    favorites = relationship("Favorite", back_populates="anime")
    comments = relationship("Comment", back_populates="anime")
    category = relationship("Category", back_populates="animes")
    tags = relationship("Tag", secondary="anime_tags", back_populates="animes")


class Category(Base):
    __tablename__ = "categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False, index=True)
    num = Column(Integer, default=0)  # 分类下视频总数
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    animes = relationship("Anime", back_populates="category")
    mangas = relationship("Manga", back_populates="category")


class Tag(Base):
    __tablename__ = "tags"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False, index=True)
    name_english = Column(String(100), nullable=True)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    animes = relationship("Anime", secondary="anime_tags", back_populates="tags")
    mangas = relationship("Manga", secondary="manga_tags", back_populates="tags")


class AnimeTag(Base):
    __tablename__ = "anime_tags"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    anime_id = Column(Integer, ForeignKey("animes.id"), nullable=False)
    tag_id = Column(Integer, ForeignKey("tags.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    __table_args__ = (
        UniqueConstraint("anime_id", "tag_id", name="uq_anime_tag"),
    )

class Favorite(Base):
    __tablename__ = "favorites"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    content_type = Column(Enum(ContentType, values_callable=lambda obj: [e.value for e in obj], native_enum=False), nullable=False, default=ContentType.ANIME)
    anime_id = Column(Integer, ForeignKey("animes.id"), nullable=True)
    manga_id = Column(BigInteger, ForeignKey("mangas.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    user = relationship("User", back_populates="favorites")
    anime = relationship("Anime", back_populates="favorites")
    manga = relationship("Manga", back_populates="favorites")

    # 确保content_type与对应的ID匹配
    __table_args__ = (
        UniqueConstraint("user_id", "content_type", "anime_id", name="uq_user_anime_favorite"),
        UniqueConstraint("user_id", "content_type", "manga_id", name="uq_user_manga_favorite"),
    )


class Comment(Base):
    __tablename__ = "comments"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    content_type = Column(Enum(ContentType, values_callable=lambda obj: [e.value for e in obj], native_enum=False), nullable=False, default=ContentType.ANIME)
    anime_id = Column(Integer, ForeignKey("animes.id"), nullable=True)
    manga_id = Column(BigInteger, ForeignKey("mangas.id"), nullable=True)
    content = Column(Text, nullable=False)
    attachments = Column(Text, nullable=True)  # JSON 数组字符串，存表情包/图片等URL
    
    # 回复功能相关字段
    parent_id = Column(Integer, ForeignKey("comments.id"), nullable=True, index=True)  # 父评论ID（用于回复）
    reply_to_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # 回复的目标用户ID
    
    # 引用功能相关字段
    quoted_comment_id = Column(Integer, ForeignKey("comments.id"), nullable=True)  # 被引用的评论ID
    quoted_content = Column(Text, nullable=True)  # 被引用评论的内容快照（防止原评论被删除后丢失引用内容）
    
    # 点赞统计字段
    like_count = Column(Integer, default=0, nullable=False)
    dislike_count = Column(Integer, default=0, nullable=False)
    
    # 编辑功能相关字段
    is_edited = Column(Boolean, default=False, nullable=False)
    edited_at = Column(DateTime(timezone=True), nullable=True)
    edit_count = Column(Integer, default=0, nullable=False)
    
    is_deleted = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    user = relationship("User", back_populates="comments", foreign_keys=[user_id])
    anime = relationship("Anime", back_populates="comments")
    manga = relationship("Manga", back_populates="comments")
    
    # 回复相关关系
    parent_comment = relationship("Comment", remote_side=[id], foreign_keys=[parent_id], backref="replies")
    reply_to_user = relationship("User", foreign_keys=[reply_to_user_id])
    
    # 引用相关关系
    quoted_comment = relationship("Comment", remote_side=[id], foreign_keys=[quoted_comment_id])
    
    # 点赞关系
    likes = relationship("CommentLike", back_populates="comment", cascade="all, delete-orphan")


class CommentLike(Base):
    __tablename__ = "comment_likes"

    id = Column(Integer, primary_key=True, index=True)
    comment_id = Column(Integer, ForeignKey("comments.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    is_like = Column(Boolean, nullable=False)  # True for like, False for dislike
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 复合唯一约束：一个用户对一个评论只能有一个投票
    __table_args__ = (
        UniqueConstraint("comment_id", "user_id", name="uq_comment_user_like"),
    )

    # 关系
    comment = relationship("Comment", back_populates="likes")
    user = relationship("User")


class SystemConfig(Base):
    __tablename__ = "system_configs"

    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(100), unique=True, nullable=False, index=True)
    value = Column(Text, nullable=True)
    description = Column(String(500), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class FeaturedAnime(Base):
    __tablename__ = "featured_animes"

    id = Column(Integer, primary_key=True, index=True)
    anime_id = Column(Integer, ForeignKey("animes.id"), nullable=False)
    order_index = Column(Integer, default=0)  # 显示顺序
    custom_poster_url = Column(String(1000), nullable=True)  # 自定义海报URL
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    anime = relationship("Anime")


# ==================== 漫画相关模型 ====================

class Manga(Base):
    __tablename__ = "mangas"

    id = Column(BigInteger, primary_key=True, index=True)
    title = Column(String(500), nullable=False, index=True)
    title_original = Column(String(500), nullable=True)
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    region_code = Column(String(10), nullable=True)  # jp/kr/cn等
    manga_type = Column(Enum(MangaType, values_callable=lambda obj: [e.value for e in obj], native_enum=False), nullable=True)
    author = Column(String(200), nullable=True)
    artist = Column(String(200), nullable=True)
    publisher = Column(String(200), nullable=True)
    description = Column(Text, nullable=True)
    cover = Column(String(1000), nullable=True)
    banner = Column(String(1000), nullable=True)
    view_count = Column(BigInteger, default=0)
    favorite_count = Column(Integer, default=0)
    chapter_count = Column(Integer, default=0)
    status = Column(Enum(MangaStatus, values_callable=lambda obj: [e.value for e in obj], native_enum=False), default=MangaStatus.ONGOING)
    is_active = Column(Boolean, default=True)
    release_date = Column(Date, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    category = relationship("Category", back_populates="mangas")
    chapters = relationship("MangaChapter", back_populates="manga", cascade="all, delete-orphan", order_by="MangaChapter.chapter_number")
    tags = relationship("Tag", secondary="manga_tags", back_populates="mangas")
    favorites = relationship("Favorite", back_populates="manga")
    comments = relationship("Comment", back_populates="manga")
    reading_progress = relationship("MangaReadingProgress", back_populates="manga", cascade="all, delete-orphan")


class MangaChapter(Base):
    __tablename__ = "manga_chapters"

    id = Column(BigInteger, primary_key=True, index=True)
    manga_id = Column(BigInteger, ForeignKey("mangas.id"), nullable=False, index=True)
    title = Column(String(300), nullable=True)
    chapter_number = Column(Numeric(8, 2), nullable=False)
    volume_number = Column(Integer, nullable=True)
    page_count = Column(Integer, default=0)
    is_free = Column(Boolean, default=True)
    price = Column(Numeric(8, 2), default=0)
    status = Column(Enum(ChapterStatus, values_callable=lambda obj: [e.value for e in obj], native_enum=False), default=ChapterStatus.PUBLISHED)
    release_date = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    manga = relationship("Manga", back_populates="chapters")
    pages = relationship("MangaPage", back_populates="chapter", cascade="all, delete-orphan", order_by="MangaPage.page_number")
    reading_progress = relationship("MangaReadingProgress", back_populates="chapter")


class MangaPage(Base):
    __tablename__ = "manga_pages"

    id = Column(BigInteger, primary_key=True, index=True)
    chapter_id = Column(BigInteger, ForeignKey("manga_chapters.id"), nullable=False, index=True)
    page_number = Column(Integer, nullable=False)
    image_url = Column(String(1000), nullable=False)
    width = Column(Integer, nullable=True)
    height = Column(Integer, nullable=True)
    file_size = Column(Integer, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系
    chapter = relationship("MangaChapter", back_populates="pages")

    # 复合唯一约束
    __table_args__ = (
        UniqueConstraint("chapter_id", "page_number", name="uq_chapter_page"),
    )


class MangaTag(Base):
    __tablename__ = "manga_tags"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    manga_id = Column(BigInteger, ForeignKey("mangas.id"), nullable=False)
    tag_id = Column(Integer, ForeignKey("tags.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    __table_args__ = (
        UniqueConstraint("manga_id", "tag_id", name="uq_manga_tag"),
    )


class MangaReadingProgress(Base):
    __tablename__ = "manga_reading_progress"

    id = Column(BigInteger, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    manga_id = Column(BigInteger, ForeignKey("mangas.id"), nullable=False, index=True)
    chapter_id = Column(BigInteger, ForeignKey("manga_chapters.id"), nullable=False)
    page_number = Column(Integer, default=1)
    total_pages = Column(Integer, default=0)
    progress_percentage = Column(Float, default=0.0)  # 阅读进度百分比
    last_read_at = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    user = relationship("User", back_populates="manga_reading_progress")
    manga = relationship("Manga", back_populates="reading_progress")
    chapter = relationship("MangaChapter", back_populates="reading_progress")

    # 复合唯一约束：一个用户对一个漫画只能有一个阅读进度记录
    __table_args__ = (
        UniqueConstraint("user_id", "manga_id", name="uq_user_manga_progress"),
    )
