'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { apiClient } from '@/lib/api';
import type { UserStats, Favorite, MangaReadingProgress } from '@/lib/api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Toast, ToastProvider } from '@/components/ui/toast';
import Link from 'next/link';
import { 
  User, 
  Heart, 
  BookOpen, 
  Clock, 
  Trash2, 
  Edit3,
  Play,
  Calendar,
  Trophy,
  Star,
  RefreshCw,
  AlertCircle,
  Shield,
  Key,
  UserX,
  Eye,
  EyeOff,
  AlertTriangle
} from 'lucide-react';

function ProfilePageContent() {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [favorites, setFavorites] = useState<Favorite[]>([]);
  const [readingHistory, setReadingHistory] = useState<MangaReadingProgress[]>([]);
  const [activeTab, setActiveTab] = useState('overview');
  const [favoritesTab, setFavoritesTab] = useState('all');
  const [editingProfile, setEditingProfile] = useState(false);
  const [profileForm, setProfileForm] = useState({
    username: '',
    avatar_url: ''
  });
  const [toastMessage, setToastMessage] = useState<{message: string, type: 'success' | 'error' | 'info'} | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [loadError, setLoadError] = useState<string | null>(null);

  // Password change state
  const [passwordForm, setPasswordForm] = useState({
    old_password: '',
    new_password: '',
    confirm_password: ''
  });
  const [showPasswords, setShowPasswords] = useState({
    old: false,
    new: false,
    confirm: false
  });
  const [passwordLoading, setPasswordLoading] = useState(false);

  // Account deletion state
  const [deletionForm, setDeletionForm] = useState({
    username_confirmation: ''
  });
  const [deletionStep, setDeletionStep] = useState(0); // 0: initial, 1: first confirm, 2: final confirm
  const [deletionLoading, setDeletionLoading] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth');
      return;
    }

    if (user) {
      setProfileForm({
        username: user.username || '',
        avatar_url: user.avatar_url || ''
      });
    }

    loadUserData();
  }, [isAuthenticated, user, router]);

  const loadUserData = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      setLoadError(null);
      
      const [stats, favs, history] = await Promise.all([
        apiClient.getUserStats().catch(err => {
          console.error('Failed to load stats:', err);
          return null;
        }),
        apiClient.getUserFavorites({ content_type: 'all', limit: 100 }).catch(err => {
          console.error('Failed to load favorites:', err);
          return [];
        }),
        apiClient.getUserReadingHistory({ limit: 20 }).catch(err => {
          console.error('Failed to load reading history:', err);
          return [];
        })
      ]);
      
      console.log('Loaded favorites:', favs);
      console.log('Manga favorites count:', favs.filter(f => f.content_type === 'manga').length);
      
      if (stats) setUserStats(stats);
      setFavorites(favs);
      setReadingHistory(history);
      
      // 如果所有数据都加载失败，显示错误
      if (!stats && favs.length === 0 && history.length === 0) {
        setLoadError('无法加载用户数据，请检查网络连接');
        setToastMessage({ message: '数据加载失败，请重试', type: 'error' });
      } else if (!stats) {
        setToastMessage({ message: '部分数据加载失败', type: 'info' });
      } else {
        setRetryCount(0); // 成功后重置重试计数
      }
    } catch (error) {
      console.error('Failed to load user data:', error);
      setLoadError('加载失败，请重试');
      setToastMessage({ message: '网络错误，请稍后重试', type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    setToastMessage({ message: `正在重试... (第${retryCount + 1}次)`, type: 'info' });
    loadUserData();
  };

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await apiClient.updateUserProfile({
        username: profileForm.username !== user?.username ? profileForm.username : undefined,
        avatar_url: profileForm.avatar_url !== user?.avatar_url ? profileForm.avatar_url : undefined
      });
      setEditingProfile(false);
      setToastMessage({ message: '个人信息更新成功', type: 'success' });
      // Refresh user data after a short delay
      setTimeout(() => window.location.reload(), 1500);
    } catch (error) {
      console.error('Failed to update profile:', error);
      setToastMessage({ message: '更新失败，请重试', type: 'error' });
    }
  };

  const handleRemoveFavorite = async (favorite: Favorite) => {
    try {
      if (favorite.anime_id) {
        await apiClient.removeFavorite('anime', favorite.anime_id);
      } else if (favorite.manga_id) {
        await apiClient.removeFavorite('manga', favorite.manga_id);
      }
      setFavorites(favorites.filter(f => f.id !== favorite.id));
      setToastMessage({ message: '已取消收藏', type: 'success' });
    } catch (error) {
      console.error('Failed to remove favorite:', error);
      setToastMessage({ message: '取消收藏失败，请重试', type: 'error' });
    }
  };

  const handleRemoveReadingHistory = async (mangaId: number) => {
    try {
      await apiClient.deleteUserReadingHistory(mangaId);
      setReadingHistory(readingHistory.filter(h => h.manga_id !== mangaId));
      setToastMessage({ message: '已删除阅读记录', type: 'success' });
    } catch (error) {
      console.error('Failed to remove reading history:', error);
      setToastMessage({ message: '删除失败，请重试', type: 'error' });
    }
  };

  // Password change handler
  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!passwordForm.old_password || !passwordForm.new_password || !passwordForm.confirm_password) {
      setToastMessage({ message: '请填写所有密码字段', type: 'error' });
      return;
    }

    if (passwordForm.new_password.length < 6) {
      setToastMessage({ message: '新密码至少需要6个字符', type: 'error' });
      return;
    }

    if (!/^(?=.*[A-Za-z])(?=.*\d)/.test(passwordForm.new_password)) {
      setToastMessage({ message: '密码必须包含至少一个字母和一个数字', type: 'error' });
      return;
    }

    if (passwordForm.new_password !== passwordForm.confirm_password) {
      setToastMessage({ message: '新密码和确认密码不匹配', type: 'error' });
      return;
    }

    try {
      setPasswordLoading(true);
      await apiClient.changePassword(passwordForm);
      setPasswordForm({
        old_password: '',
        new_password: '',
        confirm_password: ''
      });
      setToastMessage({ message: '密码修改成功', type: 'success' });
    } catch (error) {
      console.error('Failed to change password:', error);
      const errorMessage = error instanceof Error ? error.message : '修改密码失败，请重试';
      setToastMessage({ message: errorMessage, type: 'error' });
    } finally {
      setPasswordLoading(false);
    }
  };

  // Account deletion handlers
  const handleDeletionStepOne = () => {
    if (!deletionForm.username_confirmation.trim()) {
      setToastMessage({ message: '请输入用户名确认', type: 'error' });
      return;
    }
    
    if (deletionForm.username_confirmation !== user?.username) {
      setToastMessage({ message: '用户名确认不匹配', type: 'error' });
      return;
    }
    
    setDeletionStep(1);
  };

  const handleDeletionStepTwo = () => {
    setDeletionStep(2);
  };

  const handleAccountDeletion = async () => {
    try {
      setDeletionLoading(true);
      await apiClient.deleteAccount(deletionForm.username_confirmation);
      setToastMessage({ message: '账户删除成功，正在跳转...', type: 'success' });
      
      // Clear local storage and redirect after a short delay
      setTimeout(() => {
        localStorage.removeItem('access_token');
        router.push('/auth');
      }, 2000);
    } catch (error) {
      console.error('Failed to delete account:', error);
      const errorMessage = error instanceof Error ? error.message : '删除账户失败，请重试';
      setToastMessage({ message: errorMessage, type: 'error' });
      setDeletionStep(0);
      setDeletionForm({ username_confirmation: '' });
    } finally {
      setDeletionLoading(false);
    }
  };

  const resetDeletionFlow = () => {
    setDeletionStep(0);
    setDeletionForm({ username_confirmation: '' });
  };

  const filteredFavorites = favorites.filter(fav => {
    if (favoritesTab === 'all') return true;
    return fav.content_type === favoritesTab;
  });

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Toast Messages */}
      {toastMessage && (
        <Toast
          message={toastMessage.message}
          type={toastMessage.type}
          onClose={() => setToastMessage(null)}
        />
      )}
      
      {/* Error Banner with Retry */}
      {loadError && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-500" />
              <span className="text-red-700 dark:text-red-300">{loadError}</span>
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleRetry}
              className="text-red-600 hover:text-red-700"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              重试
            </Button>
          </div>
        </div>
      )}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">个人中心</h1>
        <p className="text-muted-foreground">管理您的个人信息、收藏和阅读记录</p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Trophy className="h-4 w-4" />
            概览
          </TabsTrigger>
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            个人信息
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            安全设置
          </TabsTrigger>
          <TabsTrigger value="favorites" className="flex items-center gap-2">
            <Heart className="h-4 w-4" />
            我的收藏
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            阅读历史
          </TabsTrigger>
        </TabsList>

        {/* 概览 Tab */}
        <TabsContent value="overview" className="space-y-6">
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <Skeleton className="h-8 w-full mb-4" />
                    <Skeleton className="h-4 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">总收藏</p>
                        <p className="text-2xl font-bold">{userStats?.favorites.total || 0}</p>
                      </div>
                      <Heart className="h-8 w-8 text-red-500" />
                    </div>
                    <div className="flex gap-2 mt-2">
                      <Badge variant="secondary">{userStats?.favorites.anime_count || 0} 动漫</Badge>
                      <Badge variant="secondary">{userStats?.favorites.manga_count || 0} 漫画</Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">阅读历史</p>
                        <p className="text-2xl font-bold">{userStats?.reading.manga_reading_count || 0}</p>
                      </div>
                      <BookOpen className="h-8 w-8 text-blue-500" />
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">本漫画阅读记录</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">加入时间</p>
                        <p className="text-sm font-medium">
                          {userStats?.user_info.join_date 
                            ? new Date(userStats.user_info.join_date).toLocaleDateString()
                            : '未知'
                          }
                        </p>
                      </div>
                      <Calendar className="h-8 w-8 text-green-500" />
                    </div>
                    <div className="flex gap-1 mt-2">
                      {userStats?.user_info.is_admin && (
                        <Badge variant="default">管理员</Badge>
                      )}
                      <Badge variant="outline">用户</Badge>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>最近活动</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {readingHistory.slice(0, 3).map((history) => (
                      <div key={history.id} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                        <BookOpen className="h-5 w-5 text-blue-500" />
                        <div className="flex-1">
                          <p className="font-medium">{history.manga?.title || '未知漫画'}</p>
                          <p className="text-sm text-muted-foreground">
                            第 {history.chapter?.chapter_number} 话 - 进度 {history.progress_percentage}%
                          </p>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {new Date(history.last_read_at).toLocaleDateString()}
                        </p>
                      </div>
                    ))}
                    {readingHistory.length === 0 && (
                      <p className="text-center text-muted-foreground py-8">暂无阅读记录</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        {/* 个人信息 Tab */}
        <TabsContent value="profile" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                个人信息
                <Button 
                  variant={editingProfile ? "outline" : "default"}
                  size="sm"
                  onClick={() => setEditingProfile(!editingProfile)}
                >
                  <Edit3 className="h-4 w-4 mr-2" />
                  {editingProfile ? '取消编辑' : '编辑资料'}
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {editingProfile ? (
                <form onSubmit={handleProfileUpdate} className="space-y-4">
                  <div>
                    <Label htmlFor="username">用户名</Label>
                    <Input
                      id="username"
                      value={profileForm.username}
                      onChange={(e) => setProfileForm({...profileForm, username: e.target.value})}
                      placeholder="请输入用户名"
                    />
                  </div>
                  <div>
                    <Label htmlFor="avatar">头像URL</Label>
                    <Input
                      id="avatar"
                      value={profileForm.avatar_url}
                      onChange={(e) => setProfileForm({...profileForm, avatar_url: e.target.value})}
                      placeholder="请输入头像URL（可选）"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button type="submit">保存更改</Button>
                    <Button type="button" variant="outline" onClick={() => setEditingProfile(false)}>
                      取消
                    </Button>
                  </div>
                </form>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    {user?.avatar_url ? (
                      <img src={user.avatar_url} alt="头像" className="w-16 h-16 rounded-full object-cover" />
                    ) : (
                      <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
                        <User className="h-8 w-8 text-primary-foreground" />
                      </div>
                    )}
                    <div>
                      <h3 className="text-xl font-semibold">{user?.username}</h3>
                      <p className="text-muted-foreground">{user?.email}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 pt-4">
                    <div>
                      <Label>注册时间</Label>
                      <p className="text-sm">{user?.created_at ? new Date(user.created_at).toLocaleDateString() : '未知'}</p>
                    </div>
                    <div>
                      <Label>账户状态</Label>
                      <div className="flex gap-2 mt-1">
                        {user?.is_admin && <Badge variant="default">管理员</Badge>}
                        <Badge variant={user?.is_active ? "default" : "destructive"}>
                          {user?.is_active ? '正常' : '已禁用'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 安全设置 Tab */}
        <TabsContent value="security" className="space-y-6">
          {/* Password Change Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                修改密码
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handlePasswordChange} className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="old_password">当前密码</Label>
                    <div className="relative">
                      <Input
                        id="old_password"
                        type={showPasswords.old ? "text" : "password"}
                        value={passwordForm.old_password}
                        onChange={(e) => setPasswordForm({...passwordForm, old_password: e.target.value})}
                        placeholder="请输入当前密码"
                        disabled={passwordLoading}
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPasswords({...showPasswords, old: !showPasswords.old})}
                      >
                        {showPasswords.old ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="new_password">新密码</Label>
                    <div className="relative">
                      <Input
                        id="new_password"
                        type={showPasswords.new ? "text" : "password"}
                        value={passwordForm.new_password}
                        onChange={(e) => setPasswordForm({...passwordForm, new_password: e.target.value})}
                        placeholder="请输入新密码（至少6位，包含字母和数字）"
                        disabled={passwordLoading}
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPasswords({...showPasswords, new: !showPasswords.new})}
                      >
                        {showPasswords.new ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      密码必须至少包含6个字符，包括至少一个字母和一个数字
                    </p>
                  </div>
                  
                  <div>
                    <Label htmlFor="confirm_password">确认新密码</Label>
                    <div className="relative">
                      <Input
                        id="confirm_password"
                        type={showPasswords.confirm ? "text" : "password"}
                        value={passwordForm.confirm_password}
                        onChange={(e) => setPasswordForm({...passwordForm, confirm_password: e.target.value})}
                        placeholder="请再次输入新密码"
                        disabled={passwordLoading}
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPasswords({...showPasswords, confirm: !showPasswords.confirm})}
                      >
                        {showPasswords.confirm ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
                
                <Button type="submit" disabled={passwordLoading}>
                  {passwordLoading ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      修改中...
                    </>
                  ) : (
                    <>
                      <Key className="h-4 w-4 mr-2" />
                      修改密码
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Account Deletion Section */}
          <Card className="border-red-200 dark:border-red-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-600 dark:text-red-400">
                <UserX className="h-5 w-5" />
                删除账户
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
                  <div className="space-y-2">
                    <h4 className="font-medium text-red-700 dark:text-red-300">
                      危险操作 - 此操作不可逆
                    </h4>
                    <p className="text-sm text-red-600 dark:text-red-400">
                      删除账户将永久删除您的：
                    </p>
                    <ul className="text-sm text-red-600 dark:text-red-400 list-disc list-inside space-y-1">
                      <li>个人信息和头像</li>
                      <li>所有收藏记录</li>
                      <li>阅读历史和进度</li>
                      <li>评论和互动记录</li>
                      <li>所有相关数据</li>
                    </ul>
                  </div>
                </div>
              </div>

              {deletionStep === 0 && (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="username_confirmation">
                      确认删除，请输入您的用户名：<strong>{user?.username}</strong>
                    </Label>
                    <Input
                      id="username_confirmation"
                      value={deletionForm.username_confirmation}
                      onChange={(e) => setDeletionForm({username_confirmation: e.target.value})}
                      placeholder={`请输入 "${user?.username}" 以确认删除`}
                      disabled={deletionLoading}
                    />
                  </div>
                  
                  <Button
                    variant="destructive"
                    onClick={handleDeletionStepOne}
                    disabled={deletionLoading || deletionForm.username_confirmation !== user?.username}
                  >
                    <UserX className="h-4 w-4 mr-2" />
                    继续删除账户
                  </Button>
                </div>
              )}

              {deletionStep === 1 && (
                <div className="space-y-4">
                  <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                    <div className="flex items-start gap-3">
                      <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-yellow-700 dark:text-yellow-300">
                          第二次确认
                        </h4>
                        <p className="text-sm text-yellow-600 dark:text-yellow-400">
                          您确定要删除账户 <strong>{user?.username}</strong> 吗？
                          这将删除所有相关数据，且无法恢复。
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-3">
                    <Button variant="outline" onClick={resetDeletionFlow}>
                      取消
                    </Button>
                    <Button variant="destructive" onClick={handleDeletionStepTwo}>
                      <UserX className="h-4 w-4 mr-2" />
                      我确定要删除
                    </Button>
                  </div>
                </div>
              )}

              {deletionStep === 2 && (
                <div className="space-y-4">
                  <div className="p-4 bg-red-100 dark:bg-red-900/30 border-2 border-red-300 dark:border-red-700 rounded-lg">
                    <div className="flex items-start gap-3">
                      <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-red-800 dark:text-red-200">
                          最终确认
                        </h4>
                        <p className="text-sm text-red-700 dark:text-red-300">
                          这是最后一次确认。点击下方按钮将立即删除您的账户，
                          此操作<strong>无法撤销</strong>。
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-3">
                    <Button variant="outline" onClick={resetDeletionFlow}>
                      取消删除
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={handleAccountDeletion}
                      disabled={deletionLoading}
                    >
                      {deletionLoading ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          删除中...
                        </>
                      ) : (
                        <>
                          <UserX className="h-4 w-4 mr-2" />
                          永久删除账户
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 收藏 Tab */}
        <TabsContent value="favorites" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                我的收藏 ({filteredFavorites.length})
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      setToastMessage({ message: '正在刷新数据...', type: 'info' });
                      loadUserData(false).then(() => {
                        setToastMessage({ message: '数据已刷新', type: 'success' });
                      });
                    }}
                    disabled={loading}
                  >
                    <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                  </Button>
                  <Button 
                    variant={favoritesTab === 'all' ? 'default' : 'outline'} 
                    size="sm"
                    onClick={() => setFavoritesTab('all')}
                  >
                    全部 ({favorites.length})
                  </Button>
                  <Button 
                    variant={favoritesTab === 'anime' ? 'default' : 'outline'} 
                    size="sm"
                    onClick={() => setFavoritesTab('anime')}
                  >
                    动漫 ({favorites.filter(f => f.content_type === 'anime').length})
                  </Button>
                  <Button 
                    variant={favoritesTab === 'manga' ? 'default' : 'outline'} 
                    size="sm"
                    onClick={() => setFavoritesTab('manga')}
                  >
                    漫画 ({favorites.filter(f => f.content_type === 'manga').length})
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {loading ? (
                  Array.from({ length: 6 }).map((_, i) => (
                    <Card key={i}>
                      <CardContent className="p-4">
                        <Skeleton className="h-32 w-full mb-3" />
                        <Skeleton className="h-4 w-3/4 mb-2" />
                        <Skeleton className="h-3 w-1/2" />
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  filteredFavorites.map((favorite) => {
                    const content = favorite.anime || favorite.manga;
                    const isAnime = favorite.content_type === 'anime';
                    const linkHref = isAnime ? `/anime/${content?.id}` : `/manga/${content?.id}`;
                    
                    return (
                      <Card key={favorite.id} className="group">
                        <CardContent className="p-4">
                          <div className="aspect-[3/4] bg-muted rounded-lg mb-3 overflow-hidden">
                            {content?.cover ? (
                              <img 
                                src={content.cover} 
                                alt={content.title}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                {isAnime ? <Play className="h-12 w-12 text-muted-foreground" /> : <BookOpen className="h-12 w-12 text-muted-foreground" />}
                              </div>
                            )}
                          </div>
                          <h4 className="font-medium line-clamp-2 mb-2">{content?.title || '未知标题'}</h4>
                          <div className="flex items-center justify-between">
                            <Badge variant="secondary" className="text-xs">
                              {isAnime ? '动漫' : '漫画'}
                            </Badge>
                            <div className="flex gap-1">
                              <Link href={linkHref}>
                                <Button variant="outline" size="sm">
                                  {isAnime ? <Play className="h-3 w-3" /> : <BookOpen className="h-3 w-3" />}
                                </Button>
                              </Link>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleRemoveFavorite(favorite)}
                                className="text-red-500 hover:text-red-700"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })
                )}
              </div>
              
              {!loading && filteredFavorites.length === 0 && (
                <div className="text-center py-12">
                  <Heart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">暂无收藏内容</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    去<Link href="/manga" className="text-primary hover:underline">漫画</Link>或
                    <Link href="/rifan" className="text-primary hover:underline">动漫</Link>页面收藏您喜欢的内容吧
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 阅读历史 Tab */}
        <TabsContent value="history" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                阅读历史 ({readingHistory.length})
                {readingHistory.length > 0 && (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      if (confirm('确定要清空所有阅读历史吗？')) {
                        apiClient.clearUserReadingHistory()
                          .then(() => {
                            setReadingHistory([]);
                            setToastMessage({ message: '阅读历史已清空', type: 'success' });
                          })
                          .catch(() => {
                            setToastMessage({ message: '清空失败，请重试', type: 'error' });
                          });
                      }
                    }}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    清空历史
                  </Button>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {loading ? (
                  Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-center gap-4 p-4 border rounded-lg">
                      <Skeleton className="h-16 w-12" />
                      <div className="flex-1">
                        <Skeleton className="h-4 w-3/4 mb-2" />
                        <Skeleton className="h-3 w-1/2 mb-1" />
                        <Skeleton className="h-3 w-1/3" />
                      </div>
                      <Skeleton className="h-8 w-16" />
                    </div>
                  ))
                ) : (
                  readingHistory.map((history) => (
                    <div key={history.id} className="flex items-center gap-4 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                      <div className="w-12 h-16 bg-muted rounded overflow-hidden flex-shrink-0">
                        {history.manga?.cover ? (
                          <img src={history.manga.cover} alt={history.manga.title} className="w-full h-full object-cover" />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <BookOpen className="h-6 w-6 text-muted-foreground" />
                          </div>
                        )}
                      </div>
                      
                      <div className="flex-1">
                        <h4 className="font-medium mb-1">{history.manga?.title || '未知漫画'}</h4>
                        <p className="text-sm text-muted-foreground mb-1">
                          第 {history.chapter?.chapter_number} 话
                          {history.chapter?.title && ` - ${history.chapter.title}`}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>进度: {history.progress_percentage}% ({history.page_number}/{history.total_pages})</span>
                          <span>最后阅读: {new Date(history.last_read_at).toLocaleDateString()}</span>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Link href={`/manga/${history.manga_id}/chapter/${history.chapter_id}`}>
                          <Button variant="outline" size="sm">
                            <BookOpen className="h-3 w-3 mr-1" />
                            继续阅读
                          </Button>
                        </Link>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRemoveReadingHistory(history.manga_id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </div>
              
              {!loading && readingHistory.length === 0 && (
                <div className="text-center py-12">
                  <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">暂无阅读历史</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    去<Link href="/manga" className="text-primary hover:underline">漫画页面</Link>开始阅读吧
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default function ProfilePage() {
  return (
    <ToastProvider>
      <ProfilePageContent />
    </ToastProvider>
  );
}