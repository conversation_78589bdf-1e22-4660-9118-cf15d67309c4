import { test, expect, Page, BrowserContext } from '@playwright/test'

// Test data constants
const TEST_USER = {
  username: 'e2euser',
  email: '<EMAIL>',
  password: 'password123',
  newPassword: 'newpassword456',
}

class UserManagementPage {
  constructor(private page: Page) {}

  // Navigation
  async goto() {
    await this.page.goto('/profile')
    await this.page.waitForLoadState('networkidle')
  }

  async gotoAuth() {
    await this.page.goto('/auth')
    await this.page.waitForLoadState('networkidle')
  }

  // Authentication helpers
  async login(username: string, password: string) {
    await this.gotoAuth()
    
    // Click login tab if not active
    const loginTab = this.page.getByRole('tab', { name: '登录' })
    if (await loginTab.isVisible()) {
      await loginTab.click()
    }
    
    await this.page.getByLabel('用户名').fill(username)
    await this.page.getByLabel('密码').fill(password)
    await this.page.getByRole('button', { name: '登录' }).click()
    
    // Wait for navigation to complete
    await this.page.waitForURL('/')
  }

  async register(username: string, email: string, password: string) {
    await this.gotoAuth()
    
    // Click register tab
    await this.page.getByRole('tab', { name: '注册' }).click()
    
    await this.page.getByLabel('用户名').fill(username)
    await this.page.getByLabel('邮箱').fill(email)
    await this.page.getByLabel('密码').fill(password)
    await this.page.getByRole('button', { name: '注册' }).click()
    
    // Wait for navigation
    await this.page.waitForURL('/')
  }

  async logout() {
    // Assuming there's a logout button in the navbar
    await this.page.getByRole('button', { name: '退出登录' }).click()
    await this.page.waitForURL('/auth')
  }

  // Password change helpers
  async navigateToPasswordChange() {
    await this.goto()
    await this.page.getByRole('tab', { name: '安全设置' }).click()
    await expect(this.page.getByText('修改密码')).toBeVisible()
  }

  async fillPasswordForm(oldPassword: string, newPassword: string, confirmPassword: string) {
    await this.page.getByLabel('当前密码').fill(oldPassword)
    await this.page.getByLabel('新密码').fill(newPassword)
    await this.page.getByLabel('确认新密码').fill(confirmPassword)
  }

  async submitPasswordChange() {
    await this.page.getByRole('button', { name: '修改密码' }).click()
  }

  async togglePasswordVisibility(fieldType: 'old' | 'new' | 'confirm') {
    const toggleButton = this.page.getByRole('button').filter({ 
      has: this.page.locator('.lucide-eye, .lucide-eye-off') 
    })
    
    // Click the appropriate toggle button based on position
    const buttons = await toggleButton.all()
    const index = fieldType === 'old' ? 0 : fieldType === 'new' ? 1 : 2
    if (buttons[index]) {
      await buttons[index].click()
    }
  }

  // Account deletion helpers
  async navigateToAccountDeletion() {
    await this.goto()
    await this.page.getByRole('tab', { name: '安全设置' }).click()
    await expect(this.page.getByText('删除账户')).toBeVisible()
  }

  async fillUsernameConfirmation(username: string) {
    await this.page.getByLabel(/确认删除，请输入您的用户名/).fill(username)
  }

  async clickContinueDeletion() {
    await this.page.getByRole('button', { name: '继续删除账户' }).click()
  }

  async clickConfirmDeletion() {
    await this.page.getByRole('button', { name: '我确定要删除' }).click()
  }

  async clickFinalDeletion() {
    await this.page.getByRole('button', { name: '永久删除账户' }).click()
  }

  async clickCancel() {
    await this.page.getByRole('button', { name: '取消' }).click()
  }

  // Assertions
  async expectToastMessage(message: string, type: 'success' | 'error' | 'info' = 'success') {
    const toast = this.page.getByText(message)
    await expect(toast).toBeVisible()
    
    // Wait for toast to disappear or timeout
    await expect(toast).toBeHidden({ timeout: 10000 })
  }

  async expectPasswordFieldType(fieldLabel: string, type: 'password' | 'text') {
    const field = this.page.getByLabel(fieldLabel)
    await expect(field).toHaveAttribute('type', type)
  }

  async expectButtonDisabled(buttonName: string) {
    const button = this.page.getByRole('button', { name: buttonName })
    await expect(button).toBeDisabled()
  }

  async expectButtonEnabled(buttonName: string) {
    const button = this.page.getByRole('button', { name: buttonName })
    await expect(button).toBeEnabled()
  }
}

test.describe('User Management E2E Tests', () => {
  let context: BrowserContext
  let page: Page
  let userManagementPage: UserManagementPage

  test.beforeAll(async ({ browser }) => {
    context = await browser.newContext()
    page = await context.newPage()
    userManagementPage = new UserManagementPage(page)
  })

  test.afterAll(async () => {
    await context.close()
  })

  test.describe('Authentication Flow', () => {
    test('should register a new user successfully', async () => {
      await userManagementPage.register(
        TEST_USER.username,
        TEST_USER.email,
        TEST_USER.password
      )

      // Should be redirected to home page after successful registration
      await expect(page).toHaveURL('/')
      
      // Should see user profile link or indicator
      await expect(page.getByText(TEST_USER.username)).toBeVisible()
    })

    test('should login with registered user', async () => {
      await userManagementPage.logout()
      await userManagementPage.login(TEST_USER.username, TEST_USER.password)
      
      await expect(page).toHaveURL('/')
      await expect(page.getByText(TEST_USER.username)).toBeVisible()
    })
  })

  test.describe('Password Change Flow', () => {
    test.beforeEach(async () => {
      // Ensure user is logged in
      await userManagementPage.login(TEST_USER.username, TEST_USER.password)
      await userManagementPage.navigateToPasswordChange()
    })

    test('should change password successfully with valid data', async () => {
      await userManagementPage.fillPasswordForm(
        TEST_USER.password,
        TEST_USER.newPassword,
        TEST_USER.newPassword
      )

      await userManagementPage.submitPasswordChange()

      await userManagementPage.expectToastMessage('密码修改成功', 'success')

      // Verify the form is cleared after successful change
      await expect(page.getByLabel('当前密码')).toHaveValue('')
      await expect(page.getByLabel('新密码')).toHaveValue('')
      await expect(page.getByLabel('确认新密码')).toHaveValue('')
    })

    test('should show error for wrong old password', async () => {
      await userManagementPage.fillPasswordForm(
        'wrongpassword',
        'newpassword789',
        'newpassword789'
      )

      await userManagementPage.submitPasswordChange()

      await userManagementPage.expectToastMessage('原密码错误', 'error')
    })

    test('should show validation error for short password', async () => {
      await userManagementPage.fillPasswordForm(
        TEST_USER.newPassword,
        '123', // Too short
        '123'
      )

      await userManagementPage.submitPasswordChange()

      await userManagementPage.expectToastMessage('新密码至少需要6个字符', 'error')
    })

    test('should show validation error for password without letters', async () => {
      await userManagementPage.fillPasswordForm(
        TEST_USER.newPassword,
        '123456', // Only numbers
        '123456'
      )

      await userManagementPage.submitPasswordChange()

      await userManagementPage.expectToastMessage('密码必须包含至少一个字母和一个数字', 'error')
    })

    test('should show validation error for password mismatch', async () => {
      await userManagementPage.fillPasswordForm(
        TEST_USER.newPassword,
        'newpassword789',
        'differentpassword123'
      )

      await userManagementPage.submitPasswordChange()

      await userManagementPage.expectToastMessage('新密码和确认密码不匹配', 'error')
    })

    test('should toggle password visibility', async () => {
      await userManagementPage.fillPasswordForm('test', 'test', 'test')

      // Initially passwords should be hidden
      await userManagementPage.expectPasswordFieldType('当前密码', 'password')
      await userManagementPage.expectPasswordFieldType('新密码', 'password')

      // Toggle visibility
      await userManagementPage.togglePasswordVisibility('old')
      await userManagementPage.expectPasswordFieldType('当前密码', 'text')

      await userManagementPage.togglePasswordVisibility('new')
      await userManagementPage.expectPasswordFieldType('新密码', 'text')

      // Toggle back to hidden
      await userManagementPage.togglePasswordVisibility('old')
      await userManagementPage.expectPasswordFieldType('当前密码', 'password')
    })

    test('should show loading state during password change', async () => {
      await userManagementPage.fillPasswordForm(
        TEST_USER.newPassword,
        'anotherpassword456',
        'anotherpassword456'
      )

      // Start the submission and immediately check for loading state
      const submitPromise = userManagementPage.submitPasswordChange()
      
      // Check for loading state
      await expect(page.getByText('修改中...')).toBeVisible()
      await userManagementPage.expectButtonDisabled('修改中...')

      await submitPromise
    })
  })

  test.describe('Account Deletion Flow', () => {
    test.beforeEach(async () => {
      // Create a fresh test user for deletion tests
      const deleteUser = {
        username: 'deleteuser' + Date.now(),
        email: 'delete' + Date.now() + '@test.com',
        password: 'password123',
      }

      await userManagementPage.register(
        deleteUser.username,
        deleteUser.email,
        deleteUser.password
      )
      
      await userManagementPage.navigateToAccountDeletion()
    })

    test('should display deletion warnings and requirements', async () => {
      await expect(page.getByText('危险操作 - 此操作不可逆')).toBeVisible()
      await expect(page.getByText('删除账户将永久删除您的：')).toBeVisible()
      await expect(page.getByText('个人信息和头像')).toBeVisible()
      await expect(page.getByText('所有收藏记录')).toBeVisible()
      await expect(page.getByText('阅读历史和进度')).toBeVisible()
      await expect(page.getByText('评论和互动记录')).toBeVisible()
    })

    test('should require exact username confirmation', async () => {
      // Button should be disabled initially
      await userManagementPage.expectButtonDisabled('继续删除账户')

      // Try with wrong username
      await userManagementPage.fillUsernameConfirmation('wronguser')
      await userManagementPage.clickContinueDeletion()
      await userManagementPage.expectToastMessage('用户名确认不匹配', 'error')
    })

    test('should proceed through deletion steps with correct username', async () => {
      // Get the current username from the page
      const usernameElement = page.locator('strong').first()
      const username = await usernameElement.textContent()
      
      if (!username) {
        throw new Error('Could not find username on page')
      }

      // Step 1: Username confirmation
      await userManagementPage.fillUsernameConfirmation(username)
      await userManagementPage.clickContinueDeletion()

      // Step 2: Second confirmation
      await expect(page.getByText('第二次确认')).toBeVisible()
      await expect(page.getByText(`您确定要删除账户 ${username} 吗？`)).toBeVisible()
      
      await userManagementPage.clickConfirmDeletion()

      // Step 3: Final confirmation
      await expect(page.getByText('最终确认')).toBeVisible()
      await expect(page.getByText('这是最后一次确认')).toBeVisible()
      await expect(page.getByText('无法撤销')).toBeVisible()
    })

    test('should allow cancellation at any step', async () => {
      const usernameElement = page.locator('strong').first()
      const username = await usernameElement.textContent()
      
      if (!username) {
        throw new Error('Could not find username on page')
      }

      // Go to step 1
      await userManagementPage.fillUsernameConfirmation(username)
      await userManagementPage.clickContinueDeletion()

      // Cancel from step 1
      await userManagementPage.clickCancel()
      
      // Should be back to initial state
      await expect(page.getByLabel(/确认删除，请输入您的用户名/)).toHaveValue('')

      // Go to step 2
      await userManagementPage.fillUsernameConfirmation(username)
      await userManagementPage.clickContinueDeletion()
      await userManagementPage.clickConfirmDeletion()

      // Cancel from step 2
      await userManagementPage.clickCancel()
      
      // Should be back to initial state
      await expect(page.getByLabel(/确认删除，请输入您的用户名/)).toHaveValue('')
    })

    test('should complete account deletion successfully', async () => {
      const usernameElement = page.locator('strong').first()
      const username = await usernameElement.textContent()
      
      if (!username) {
        throw new Error('Could not find username on page')
      }

      // Complete all steps
      await userManagementPage.fillUsernameConfirmation(username)
      await userManagementPage.clickContinueDeletion()
      await userManagementPage.clickConfirmDeletion()

      // Final deletion - this should redirect to auth page
      await userManagementPage.clickFinalDeletion()

      await userManagementPage.expectToastMessage('账户删除成功，正在跳转...', 'success')

      // Should be redirected to auth page
      await expect(page).toHaveURL('/auth', { timeout: 5000 })

      // Should not be able to login with deleted account
      await userManagementPage.login(username, 'password123')
      
      // Should show error or stay on auth page
      await expect(page).toHaveURL('/auth')
    })
  })

  test.describe('Security and Edge Cases', () => {
    test('should prevent access to profile page when not authenticated', async () => {
      // Ensure user is logged out
      await userManagementPage.gotoAuth()
      
      // Try to access profile page directly
      await page.goto('/profile')
      
      // Should be redirected to auth page
      await expect(page).toHaveURL('/auth')
    })

    test('should handle session expiration gracefully', async () => {
      await userManagementPage.login(TEST_USER.username, TEST_USER.newPassword)
      
      // Simulate session expiration by removing token
      await page.evaluate(() => localStorage.removeItem('access_token'))
      
      // Try to access protected functionality
      await userManagementPage.goto()
      
      // Should be redirected to auth
      await expect(page).toHaveURL('/auth')
    })

    test('should validate form fields are properly disabled during loading', async () => {
      await userManagementPage.login(TEST_USER.username, TEST_USER.newPassword)
      await userManagementPage.navigateToPasswordChange()

      // Fill form and start submission
      await userManagementPage.fillPasswordForm(
        TEST_USER.newPassword,
        'finalpassword789',
        'finalpassword789'
      )

      const submitPromise = userManagementPage.submitPasswordChange()

      // Check that all form fields are disabled during loading
      await expect(page.getByLabel('当前密码')).toBeDisabled()
      await expect(page.getByLabel('新密码')).toBeDisabled()
      await expect(page.getByLabel('确认新密码')).toBeDisabled()

      await submitPromise
    })
  })

  test.describe('Mobile Responsive', () => {
    test('should work correctly on mobile viewport', async () => {
      await page.setViewportSize({ width: 375, height: 667 })
      
      await userManagementPage.login(TEST_USER.username, TEST_USER.newPassword)
      await userManagementPage.navigateToPasswordChange()

      // Should still be able to see and interact with form elements
      await expect(page.getByLabel('当前密码')).toBeVisible()
      await expect(page.getByLabel('新密码')).toBeVisible()
      await expect(page.getByLabel('确认新密码')).toBeVisible()

      // Form should still work
      await userManagementPage.fillPasswordForm('test', 'test123', 'test456')
      await userManagementPage.submitPasswordChange()
      
      await userManagementPage.expectToastMessage('新密码和确认密码不匹配', 'error')
    })
  })
})