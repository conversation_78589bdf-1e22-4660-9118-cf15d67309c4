"""
系统配置管理模块
提供配置的默认值、类型定义和管理功能
"""
from enum import Enum
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from app.models import SystemConfig


class ConfigType(str, Enum):
    """配置类型枚举"""
    SITE_INFO = "site_info"
    SMTP_CONFIG = "smtp_config"
    USER_MANAGEMENT = "user_management"
    PLAYER_CONFIG = "player_config"


class ConfigCategory(str, Enum):
    """配置分类，用于UI展示"""
    GENERAL = "general"
    EMAIL = "email"
    USERS = "users"
    PLAYER = "player"


# 默认配置定义
DEFAULT_CONFIGS = {
    # 网站基本信息
    "site_name": {
        "value": "动漫网站",
        "type": ConfigType.SITE_INFO,
        "category": ConfigCategory.GENERAL,
        "description": "网站名称",
        "required": True
    },
    "site_description": {
        "value": "最新动漫资源分享平台",
        "type": ConfigType.SITE_INFO,
        "category": ConfigCategory.GENERAL,
        "description": "网站描述",
        "required": False
    },
    "site_keywords": {
        "value": "动漫,视频,在线观看",
        "type": ConfigType.SITE_INFO,
        "category": ConfigCategory.GENERAL,
        "description": "网站关键词（SEO用）",
        "required": False
    },
    "contact_email": {
        "value": "<EMAIL>",
        "type": ConfigType.SITE_INFO,
        "category": ConfigCategory.GENERAL,
        "description": "联系邮箱",
        "required": True
    },
    "site_logo_url": {
        "value": "",
        "type": ConfigType.SITE_INFO,
        "category": ConfigCategory.GENERAL,
        "description": "网站Logo URL",
        "required": False
    },
    
    # SMTP邮件配置
    "smtp_host": {
        "value": "",
        "type": ConfigType.SMTP_CONFIG,
        "category": ConfigCategory.EMAIL,
        "description": "SMTP服务器地址",
        "required": True
    },
    "smtp_port": {
        "value": "587",
        "type": ConfigType.SMTP_CONFIG,
        "category": ConfigCategory.EMAIL,
        "description": "SMTP端口",
        "required": True
    },
    "smtp_use_tls": {
        "value": "true",
        "type": ConfigType.SMTP_CONFIG,
        "category": ConfigCategory.EMAIL,
        "description": "使用TLS加密",
        "required": True
    },
    "smtp_use_ssl": {
        "value": "false",
        "type": ConfigType.SMTP_CONFIG,
        "category": ConfigCategory.EMAIL,
        "description": "使用SSL加密",
        "required": True
    },
    "smtp_username": {
        "value": "",
        "type": ConfigType.SMTP_CONFIG,
        "category": ConfigCategory.EMAIL,
        "description": "SMTP用户名",
        "required": True
    },
    "smtp_password": {
        "value": "",
        "type": ConfigType.SMTP_CONFIG,
        "category": ConfigCategory.EMAIL,
        "description": "SMTP密码",
        "required": True,
        "sensitive": True
    },
    "email_from_name": {
        "value": "动漫网站",
        "type": ConfigType.SMTP_CONFIG,
        "category": ConfigCategory.EMAIL,
        "description": "发件人名称",
        "required": True
    },
    "email_from_address": {
        "value": "",
        "type": ConfigType.SMTP_CONFIG,
        "category": ConfigCategory.EMAIL,
        "description": "发件人邮箱地址",
        "required": True
    },
    
    # 用户管理配置
    "allow_registration": {
        "value": "true",
        "type": ConfigType.USER_MANAGEMENT,
        "category": ConfigCategory.USERS,
        "description": "允许用户注册",
        "required": True
    },
    "require_email_verification": {
        "value": "false",
        "type": ConfigType.USER_MANAGEMENT,
        "category": ConfigCategory.USERS,
        "description": "注册时需要邮箱验证码",
        "required": True
    },
    "enable_email_login": {
        "value": "false",
        "type": ConfigType.USER_MANAGEMENT,
        "category": ConfigCategory.USERS,
        "description": "启用邮箱验证码登录",
        "required": True
    },
    "enable_turnstile": {
        "value": "false",
        "type": ConfigType.USER_MANAGEMENT,
        "category": ConfigCategory.USERS,
        "description": "启用Cloudflare Turnstile验证",
        "required": True
    },
    "turnstile_site_key": {
        "value": "",
        "type": ConfigType.USER_MANAGEMENT,
        "category": ConfigCategory.USERS,
        "description": "Turnstile站点密钥",
        "required": False
    },
    "turnstile_secret_key": {
        "value": "",
        "type": ConfigType.USER_MANAGEMENT,
        "category": ConfigCategory.USERS,
        "description": "Turnstile密钥",
        "required": False,
        "sensitive": True
    },
    "default_user_role": {
        "value": "user",
        "type": ConfigType.USER_MANAGEMENT,
        "category": ConfigCategory.USERS,
        "description": "默认用户角色",
        "required": True
    },
    "max_favorite_count": {
        "value": "1000",
        "type": ConfigType.USER_MANAGEMENT,
        "category": ConfigCategory.USERS,
        "description": "用户最大收藏数量",
        "required": False
    },
    
    # 视频播放器配置
    "player_enable_ads": {
        "value": "false",
        "type": ConfigType.PLAYER_CONFIG,
        "category": ConfigCategory.PLAYER,
        "description": "启用视频广告",
        "required": True
    },
    "player_preroll_ad_url": {
        "value": "",
        "type": ConfigType.PLAYER_CONFIG,
        "category": ConfigCategory.PLAYER,
        "description": "前贴片广告视频URL",
        "required": False
    },
    "player_midroll_ad_url": {
        "value": "",
        "type": ConfigType.PLAYER_CONFIG,
        "category": ConfigCategory.PLAYER,
        "description": "中插广告视频URL",
        "required": False
    },
    "player_postroll_ad_url": {
        "value": "",
        "type": ConfigType.PLAYER_CONFIG,
        "category": ConfigCategory.PLAYER,
        "description": "后贴片广告视频URL",
        "required": False
    },
    "player_enable_vast": {
        "value": "false",
        "type": ConfigType.PLAYER_CONFIG,
        "category": ConfigCategory.PLAYER,
        "description": "启用VAST广告",
        "required": True
    },
    "player_vast_url": {
        "value": "",
        "type": ConfigType.PLAYER_CONFIG,
        "category": ConfigCategory.PLAYER,
        "description": "VAST广告配置URL",
        "required": False
    },
    "player_enable_rightclick": {
        "value": "false",
        "type": ConfigType.PLAYER_CONFIG,
        "category": ConfigCategory.PLAYER,
        "description": "启用播放器右键菜单",
        "required": True
    },
    "player_show_stats": {
        "value": "false",
        "type": ConfigType.PLAYER_CONFIG,
        "category": ConfigCategory.PLAYER,
        "description": "显示播放器统计信息",
        "required": True
    },
    "player_show_version": {
        "value": "false",
        "type": ConfigType.PLAYER_CONFIG,
        "category": ConfigCategory.PLAYER,
        "description": "显示播放器版本信息",
        "required": True
    },
    "player_skip_ad_time": {
        "value": "5",
        "type": ConfigType.PLAYER_CONFIG,
        "category": ConfigCategory.PLAYER,
        "description": "广告跳过时间（秒）",
        "required": False
    },
    "player_ad_volume": {
        "value": "0.7",
        "type": ConfigType.PLAYER_CONFIG,
        "category": ConfigCategory.PLAYER,
        "description": "广告音量（0-1）",
        "required": False
    },
    "player_autoplay": {
        "value": "false",
        "type": ConfigType.PLAYER_CONFIG,
        "category": ConfigCategory.PLAYER,
        "description": "自动播放视频",
        "required": True
    },
    "player_theme_color": {
        "value": "#7c3aed",
        "type": ConfigType.PLAYER_CONFIG,
        "category": ConfigCategory.PLAYER,
        "description": "播放器主题颜色",
        "required": False
    }
}


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_config(self, key: str, default_value: str = None) -> Optional[str]:
        """获取单个配置值"""
        config = self.db.query(SystemConfig).filter(SystemConfig.key == key).first()
        if config:
            return config.value
        return default_value or DEFAULT_CONFIGS.get(key, {}).get("value")
    
    def set_config(self, key: str, value: str, description: str = None) -> SystemConfig:
        """设置单个配置值"""
        config = self.db.query(SystemConfig).filter(SystemConfig.key == key).first()
        if config:
            config.value = value
            if description:
                config.description = description
        else:
            config = SystemConfig(
                key=key,
                value=value,
                description=description or DEFAULT_CONFIGS.get(key, {}).get("description", "")
            )
            self.db.add(config)
        
        self.db.commit()
        self.db.refresh(config)
        return config
    
    def get_configs_by_type(self, config_type: ConfigType) -> Dict[str, Any]:
        """根据类型获取配置"""
        configs = {}
        
        # 获取该类型的所有默认配置key
        default_keys = [key for key, meta in DEFAULT_CONFIGS.items() 
                       if meta.get("type") == config_type]
        
        # 从数据库获取现有配置
        db_configs = self.db.query(SystemConfig).filter(
            SystemConfig.key.in_(default_keys)
        ).all()
        
        db_config_dict = {config.key: config for config in db_configs}
        
        # 合并默认配置和数据库配置
        for key in default_keys:
            if key in db_config_dict:
                config = db_config_dict[key]
                configs[key] = {
                    "value": config.value,
                    "description": config.description,
                    **DEFAULT_CONFIGS[key]
                }
            else:
                configs[key] = DEFAULT_CONFIGS[key].copy()
        
        return configs
    
    def get_configs_by_category(self, category: ConfigCategory) -> Dict[str, Any]:
        """根据分类获取配置"""
        configs = {}
        
        # 获取该分类的所有默认配置key
        default_keys = [key for key, meta in DEFAULT_CONFIGS.items() 
                       if meta.get("category") == category]
        
        # 从数据库获取现有配置
        db_configs = self.db.query(SystemConfig).filter(
            SystemConfig.key.in_(default_keys)
        ).all()
        
        db_config_dict = {config.key: config for config in db_configs}
        
        # 合并默认配置和数据库配置
        for key in default_keys:
            if key in db_config_dict:
                config = db_config_dict[key]
                configs[key] = {
                    "value": config.value,
                    "description": config.description,
                    **DEFAULT_CONFIGS[key]
                }
            else:
                configs[key] = DEFAULT_CONFIGS[key].copy()
        
        return configs
    
    def init_default_configs(self) -> List[SystemConfig]:
        """初始化默认配置到数据库"""
        created_configs = []
        
        for key, meta in DEFAULT_CONFIGS.items():
            existing = self.db.query(SystemConfig).filter(SystemConfig.key == key).first()
            if not existing:
                config = SystemConfig(
                    key=key,
                    value=meta["value"],
                    description=meta["description"]
                )
                self.db.add(config)
                created_configs.append(config)
        
        self.db.commit()
        return created_configs
    
    def update_configs_batch(self, configs: Dict[str, str]) -> List[SystemConfig]:
        """批量更新配置"""
        updated_configs = []
        
        for key, value in configs.items():
            if key in DEFAULT_CONFIGS:
                config = self.set_config(key, value)
                updated_configs.append(config)
        
        return updated_configs
    
    def get_smtp_config(self) -> Dict[str, Any]:
        """获取SMTP配置用于邮件发送"""
        smtp_configs = self.get_configs_by_type(ConfigType.SMTP_CONFIG)
        
        return {
            "host": smtp_configs.get("smtp_host", {}).get("value", ""),
            "port": int(smtp_configs.get("smtp_port", {}).get("value", "587")),
            "use_tls": smtp_configs.get("smtp_use_tls", {}).get("value", "true").lower() == "true",
            "use_ssl": smtp_configs.get("smtp_use_ssl", {}).get("value", "false").lower() == "true",
            "username": smtp_configs.get("smtp_username", {}).get("value", ""),
            "password": smtp_configs.get("smtp_password", {}).get("value", ""),
            "from_name": smtp_configs.get("email_from_name", {}).get("value", ""),
            "from_address": smtp_configs.get("email_from_address", {}).get("value", "")
        }
    
    def get_bool_config(self, key: str, default: bool = False) -> bool:
        """获取布尔类型配置"""
        value = self.get_config(key)
        if value is None:
            return default
        return value.lower() in ("true", "1", "yes", "on")
    
    def get_player_config(self) -> Dict[str, Any]:
        """获取播放器配置"""
        player_configs = self.get_configs_by_type(ConfigType.PLAYER_CONFIG)
        
        return {
            "enable_ads": self.get_bool_config("player_enable_ads", False),
            "preroll_ad_url": self.get_config("player_preroll_ad_url", ""),
            "midroll_ad_url": self.get_config("player_midroll_ad_url", ""),
            "postroll_ad_url": self.get_config("player_postroll_ad_url", ""),
            "enable_vast": self.get_bool_config("player_enable_vast", False),
            "vast_url": self.get_config("player_vast_url", ""),
            "enable_rightclick": self.get_bool_config("player_enable_rightclick", False),
            "show_stats": self.get_bool_config("player_show_stats", False),
            "show_version": self.get_bool_config("player_show_version", False),
            "skip_ad_time": self.get_int_config("player_skip_ad_time", 5),
            "ad_volume": float(self.get_config("player_ad_volume", "0.7")),
            "autoplay": self.get_bool_config("player_autoplay", False),
            "theme_color": self.get_config("player_theme_color", "#7c3aed")
        }
    
    def get_int_config(self, key: str, default: int = 0) -> int:
        """获取整数类型配置"""
        value = self.get_config(key)
        if value is None:
            return default
        try:
            return int(value)
        except ValueError:
            return default
    
    def get_float_config(self, key: str, default: float = 0.0) -> float:
        """获取浮点数类型配置"""
        value = self.get_config(key)
        if value is None:
            return default
        try:
            return float(value)
        except ValueError:
            return default