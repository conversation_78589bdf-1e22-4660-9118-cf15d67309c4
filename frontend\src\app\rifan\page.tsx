'use client';

import { useState, useEffect } from 'react';
import { AnimeCard } from '@/components/anime/AnimeCard';
import { Pagination } from '@/components/ui/Pagination';
import { Anime, apiClient } from '@/lib/api';

export default function RifanPage() {
  const [animes, setAnimes] = useState<Anime[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalResults, setTotalResults] = useState(0);

  useEffect(() => {
    fetchRifanAnimes();
  }, [currentPage]);

  const fetchRifanAnimes = async () => {
    try {
      setLoading(true);
      // 计算skip值：(页码-1) * 每页数量
      const skip = (currentPage - 1) * 42;
      
      // 使用分类ID=1查询里番分类的动漫
      const response = await apiClient.getAnimes({
        skip: skip,
        limit: 42, // 7列×6行 = 42个视频
        category_id: 1, // 里番分类ID
        sort_by: 'created_at',
        sort_order: 'desc'
      });
      
      console.log('Rifan API Response:', response); // 调试日志
      
      setAnimes(response.animes || []);
      setTotalPages(Math.ceil((response.total || 0) / 42));
      setTotalResults(response.total || 0);
    } catch (error) {
      console.error('Failed to fetch rifan animes:', error);
      setAnimes([]);
      setTotalResults(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  const handleFavoriteChange = () => {
    fetchRifanAnimes();
  };

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Page Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">里番</h1>
        <p className="text-muted-foreground">
          共 {totalResults} 个内容
        </p>
      </div>

      {/* Content */}
      {loading ? (
        <div className="text-center py-12">
          <div className="text-lg">加载中...</div>
        </div>
      ) : animes && animes.length > 0 ? (
        <>
          {/* Results Grid - 7 columns */}
          <div className="grid grid-cols-3 md:grid-cols-7 gap-4">
            {animes.map((anime) => (
              <AnimeCard 
                key={anime.id} 
                anime={anime} 
                onFavoriteChange={handleFavoriteChange} 
              />
            ))}
          </div>

          {/* Advanced Pagination */}
          {totalPages > 1 && (
            <div className="mt-8">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
              
              {/* 页面信息 - 移动端缩小 */}
              <div className="flex justify-center items-center mt-4">
                <span className="text-xs md:text-sm text-muted-foreground">
                  第 {currentPage} 页，共 {totalPages} 页 • 共 {totalResults} 个结果
                </span>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12">
          <div className="text-lg mb-2">暂无内容</div>
          <p className="text-muted-foreground">
            没有找到里番相关的内容
          </p>
        </div>
      )}
    </div>
  );
}