import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Device detection utility functions
export function isMobile(): boolean {
  if (typeof window === 'undefined') return false;
  return /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
         window.innerWidth <= 768;
}

export function isTouchDevice(): boolean {
  if (typeof window === 'undefined') return false;
  return 'ontouchstart' in window || 
         navigator.maxTouchPoints > 0 || 
         (navigator as any).msMaxTouchPoints > 0;
}

export function isSlowNetwork(): boolean {
  if (typeof navigator === 'undefined' || !('connection' in navigator)) return false;
  const connection = (navigator as any).connection;
  if (!connection) return false;
  
  // Consider 2G or slow-3g as slow networks
  const slowConnections = ['slow-2g', '2g'];
  return slowConnections.includes(connection.effectiveType) || 
         (connection.downlink && connection.downlink < 1.5); // Less than 1.5 Mbps
}