import type { Metadata } from "next";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { Navbar } from "@/components/Navbar";
import { PagePersistence } from "@/components/PagePersistence";

export const metadata: Metadata = {
  title: "视频站",
  description: "私有视频浏览站点",
  formatDetection: {
    telephone: false,
    date: false,
    address: false,
    email: false,
    url: false
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "视频站"
  },
  other: {
    "mobile-web-app-capable": "yes",
    "apple-mobile-web-app-capable": "yes",
    "apple-mobile-web-app-status-bar-style": "default",
    "theme-color": "#000000",
    "msapplication-TileColor": "#000000",
    "msapplication-tap-highlight": "no"
  }
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: "cover" as const
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN" className="overflow-x-hidden">
      <body
        className="antialiased overflow-x-hidden font-sans"
        style={{
          textSizeAdjust: '100%',
          WebkitTextSizeAdjust: '100%',
          MozTextSizeAdjust: '100%',
        } as React.CSSProperties}
      >
        <ThemeProvider>
          <AuthProvider>
            <PagePersistence>
              <div className="min-h-screen flex flex-col overflow-x-hidden">
                <Navbar />
                <main className="flex-1 overflow-x-hidden w-full">
                  {children}
                </main>
              </div>
            </PagePersistence>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
